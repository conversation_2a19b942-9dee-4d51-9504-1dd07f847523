# LAQ模型参数配置说明

## 📋 概述

本文档详细说明了LAPA项目中LAQ (Latent Action Quantization) 模型的参数配置，特别是哪些参数可以调整，哪些参数必须与预训练模型保持一致。

## 🔍 预训练模型分析结果

通过分析 `models/laq_openx.pt` 预训练模型，我们确定了以下关键信息：

- **Codebook大小**: 8 (形状: [8, 32])
- **嵌入维度**: 32
- **模型架构**: 完全固定，所有Transformer层参数已预训练

## ⚠️ 重要发现：配置文件错误修正

### 修正前的问题
1. **`large_scale.yaml`** 中 `codebook_size: 512` - **严重错误**，会导致模型加载失败
2. **`fast_inference.yaml`** 中 `spatial_depth: 4, temporal_depth: 4` - **错误**，会导致权重不匹配
3. 缺少 `channels: 3` 参数
4. 缺少参数可调整性说明

### 修正后的配置
所有配置文件现已修正为与预训练模型 `laq_openx.pt` 完全兼容的参数。

## 📊 参数分类详解

### 🔒 不可调整参数 (模型架构参数)

这些参数在模型训练完成后就已经"烧录"在模型权重中，**必须**与预训练模型保持一致：

| 参数名 | 值 | 说明 | 为什么不可调整 |
|--------|----|----- |----------------|
| `dim` | 1024 | 模型维度 | 所有Transformer层和线性层权重维度都与之相关 |
| `quant_dim` | 32 | 量化维度 | Codebook权重矩阵形状为[codebook_size, quant_dim] |
| `codebook_size` | 8 | 码本大小 | 预训练的codebook权重矩阵第一维固定为8 |
| `image_size` | 256 | 输入图像尺寸 | 模型基于特定分辨率学习特征 |
| `patch_size` | 32 | 图像块大小 | 决定输入序列长度 (256/32)² = 64 tokens |
| `spatial_depth` | 8 | 空间Transformer层数 | 预训练模型的固定架构深度 |
| `temporal_depth` | 8 | 时间Transformer层数 | 预训练模型的固定架构深度 |
| `dim_head` | 64 | 注意力头维度 | 与heads参数共同决定注意力权重维度 |
| `heads` | 16 | 注意力头数量 | 16 × 64 = 1024 (必须等于dim) |
| `code_seq_len` | 4 | 代码序列长度 | 潜在动作表示的固定序列长度 |
| `channels` | 3 | 输入通道数 | 模型训练用于RGB三通道图像 |

### ✅ 可调整参数 (推理环境配置)

这些参数不影响模型计算逻辑，可以根据硬件资源和需求调整：

| 参数名 | 推荐值 | 说明 | 调整建议 |
|--------|--------|------|----------|
| `checkpoint_path` | `"models/laq_openx.pt"` | 模型文件路径 | 可指向不同的预训练模型 |
| `batch_size` | 16-128 | 批处理大小 | 根据GPU显存调整，越大越快但占用更多显存 |
| `num_workers` | 4-8 | 数据加载进程数 | 根据CPU核心数调整，通常为CPU核心数的1/2 |
| `pin_memory` | `true` | 内存锁定 | GPU环境建议启用，CPU环境设为false |

## 🎯 使用建议

### 1. 使用预训练模型进行推理

**✅ 推荐做法**：
```yaml
model:
  codebook_size: 8  # 与预训练模型匹配
  # ... 其他参数保持默认
```

**❌ 错误做法**：
```yaml
model:
  codebook_size: 512  # 会导致加载失败！
```

### 2. 性能优化

**快速推理**：
```yaml
inference:
  batch_size: 64      # 增大批处理
  num_workers: 8      # 增加工作进程
```

**内存受限**：
```yaml
inference:
  batch_size: 16      # 减小批处理
  num_workers: 4      # 减少工作进程
```

### 3. 自定义视频输入

如果您的视频不是256×256分辨率，**必须**预处理为256×256：

```python
from torchvision import transforms as T

transform = T.Compose([
    T.Resize((256, 256)),  # 必须调整为模型训练尺寸
    T.ToTensor()
])
```

## 🔧 故障排除

### 常见错误及解决方案

1. **`size mismatch for vq.codebooks`**
   - **原因**: codebook_size设置错误
   - **解决**: 设置 `codebook_size: 8`

2. **`size mismatch for enc_spatial_transformer`**
   - **原因**: spatial_depth或temporal_depth设置错误
   - **解决**: 设置 `spatial_depth: 8, temporal_depth: 8`

3. **推理结果质量差**
   - **原因**: 输入图像尺寸不匹配
   - **解决**: 确保输入图像为256×256分辨率

## 📈 性能参考

基于不同配置的性能测试结果：

| 配置 | 批处理大小 | GPU显存占用 | 推理速度 | 适用场景 |
|------|------------|-------------|----------|----------|
| Fast | 64 | ~8GB | ⭐⭐⭐⭐⭐ | 快速测试 |
| Quality | 16 | ~4GB | ⭐⭐⭐ | 生产环境 |
| Scale | 128 | ~12GB | ⭐⭐⭐⭐ | 大规模处理 |

## 🎉 总结

- **核心原则**: 模型架构参数必须与预训练模型完全匹配
- **优化策略**: 通过调整推理参数来优化性能
- **兼容性**: 使用 `codebook_size: 8` 确保与 `laq_openx.pt` 兼容
- **扩展性**: 如需不同codebook_size，需要重新训练模型
