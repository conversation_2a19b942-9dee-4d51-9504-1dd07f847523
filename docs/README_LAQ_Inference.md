# LAQ模型推理脚本

一个完整的LAQ（Latent Action Quantization）模型推理解决方案，支持批量处理预处理的帧对数据，生成动作量化码。

## 🚀 快速开始

### 1. 环境检查

运行测试脚本检查环境：

```bash
python3 scripts/test_laq_inference.py
```

### 2. 一键推理

使用快速启动脚本：

```bash
# 快速推理模式（推荐用于测试）
bash scripts/run_laq_inference.sh --mode fast

# 高质量推理模式（推荐用于生产）
bash scripts/run_laq_inference.sh --mode quality

# 大规模推理模式（推荐用于大数据集）
bash scripts/run_laq_inference.sh --mode scale
```

### 3. 自定义推理

```bash
python3 scripts/inference/laq_inference.py \
    --input data/smart_preprocessed_fixed/laq_dataset_hybrid.jsonl \
    --output results/laq_results.jsonl \
    --batch_size 32
```

## 📁 项目结构

```
├── scripts/
│   ├── inference/
│   │   └── laq_inference.py          # 主推理脚本
│   ├── run_laq_inference.sh          # 快速启动脚本
│   └── test_laq_inference.py         # 测试脚本
├── configs/
│   ├── laq_inference.yaml            # 默认配置
│   └── presets/                      # 预设配置
│       ├── fast_inference.yaml       # 快速推理
│       ├── high_quality.yaml         # 高质量推理
│       └── large_scale.yaml          # 大规模推理
├── docs/
│   └── LAQ_推理脚本使用指南.md        # 详细使用指南
├── data/
│   └── smart_preprocessed_fixed/     # 预处理数据
├── models/
│   └── laq_openx.pt                  # 预训练模型
├── results/                          # 推理结果
└── logs/                            # 日志文件
```

## ✨ 功能特性

- ✅ **批量处理**: 支持大规模帧对数据的批量推理
- ✅ **GPU加速**: 自动检测并使用CUDA设备
- ✅ **灵活配置**: 支持命令行参数和YAML配置文件
- ✅ **错误处理**: 完善的错误处理和重试机制
- ✅ **进度显示**: 实时显示推理进度和统计信息
- ✅ **日志系统**: 详细的日志记录和调试信息
- ✅ **预设配置**: 多种预设配置适应不同场景

## 📋 使用场景

### 快速测试 (`fast` 模式)
- 适合：功能验证、快速测试
- 特点：较小模型深度、大批处理大小
- 速度：⭐⭐⭐⭐⭐
- 质量：⭐⭐⭐

### 生产环境 (`quality` 模式)
- 适合：正式推理、高质量要求
- 特点：更深模型、更大码本
- 速度：⭐⭐⭐
- 质量：⭐⭐⭐⭐⭐

### 大规模处理 (`scale` 模式)
- 适合：大数据集、批量处理
- 特点：大批处理、多进程
- 速度：⭐⭐⭐⭐
- 质量：⭐⭐⭐⭐

## 🔧 配置说明

### 模型参数
- `codebook_size`: 码本大小 (8/512/1024)
- `spatial_depth`: 空间Transformer深度 (4-12)
- `temporal_depth`: 时间Transformer深度 (4-12)

### 推理参数
- `batch_size`: 批处理大小 (16-128)
- `num_workers`: 数据加载进程数 (4-8)

## 📊 性能参考

| 配置 | 批处理大小 | GPU内存 | 处理速度 | 适用场景 |
|------|------------|---------|----------|----------|
| Fast | 64 | ~4GB | ~100样本/秒 | 测试验证 |
| Quality | 16 | ~8GB | ~30样本/秒 | 生产环境 |
| Scale | 128 | ~6GB | ~150样本/秒 | 大规模处理 |

## 📝 输入数据格式

JSONL格式，每行一个帧对：

```json
{
  "id": "video_segment_00001",
  "image": "path/to/first_frame.jpg",
  "next_image": "path/to/second_frame.jpg",
  "instruction": "Predict the action between these two frames",
  "vision": "",
  "fields": "[instruction],[vision],delta"
}
```

## 📤 输出数据格式

JSONL格式，包含动作量化码：

```json
{
  "id": "video_segment_00001",
  "image": "path/to/first_frame.jpg",
  "next_image": "path/to/second_frame.jpg",
  "instruction": "Predict the action between these two frames",
  "vision": "",
  "delta": ["3", "7", "1", "5"],
  "fields": "[instruction],[vision],delta"
}
```

## 🛠️ 故障排除

### 常见问题

1. **CUDA内存不足**
   ```bash
   # 减小批处理大小
   --batch_size 16
   ```

2. **模型加载失败**
   ```bash
   # 检查模型路径
   ls -la models/laq_openx.pt
   ```

3. **依赖缺失**
   ```bash
   pip install torch torchvision pillow tqdm pyyaml numpy
   ```

### 调试模式

启用详细日志：

```bash
python3 scripts/inference/laq_inference.py \
    --config configs/presets/fast_inference.yaml \
    --log_level DEBUG
```

## 📚 详细文档

查看完整使用指南：[LAQ_推理脚本使用指南.md](docs/LAQ_推理脚本使用指南.md)

## 🧪 测试

运行完整测试套件：

```bash
python3 scripts/test_laq_inference.py
```

## 📈 使用示例

### 示例1: 基本推理
```bash
python3 scripts/inference/laq_inference.py \
    --input data/smart_preprocessed_fixed/laq_dataset_hybrid.jsonl \
    --output results/basic_results.jsonl
```

### 示例2: 高质量推理
```bash
bash scripts/run_laq_inference.sh \
    --mode quality \
    --input data/my_data.jsonl \
    --output results/quality_results.jsonl
```

### 示例3: 自定义配置
```bash
python3 scripts/inference/laq_inference.py \
    --config configs/my_custom_config.yaml \
    --batch_size 64
```

## 🔍 监控和日志

- 实时进度显示
- 详细错误日志
- 性能统计信息
- GPU内存监控

## 💡 优化建议

1. **GPU内存优化**: 根据显存调整批处理大小
2. **处理速度优化**: 增加工作进程数
3. **大规模处理**: 使用SSD存储，分批处理

## 📞 技术支持

如遇问题：
1. 查看日志文件获取详细信息
2. 运行测试脚本检查环境
3. 参考详细使用指南

---

*创建日期: 2025-08-01*  
*版本: 1.0.0*
