# LAQ (Latent Action Quantization) 环境使用指南

## 环境配置完成 ✅

您的LAQ环境已经成功配置完成！以下是环境的详细信息和使用方法。

## 环境信息

### 虚拟环境
- **环境名称**: `lapa_laq`
- **Python版本**: 3.9.23
- **激活命令**: `conda activate lapa_laq`

### 核心依赖版本
- **PyTorch**: 2.2.0+cu121 (支持CUDA)
- **TensorFlow**: 2.15.0
- **Transformers**: 4.40.1
- **CUDA**: 可用 (NVIDIA RTX A5000)

### 已安装的关键包
- torch, torchvision, torchaudio
- tensorflow, tensorflow-datasets, tensorflow-graphics
- transformers, tokenizers
- accelerate, wandb
- einops, vector-quantize-pytorch
- opencv-python, pillow
- 以及所有LAQ模块的依赖

## 使用方法

### 1. 激活环境
```bash
conda activate lapa_laq
```

### 2. 进入LAQ目录
```bash
cd /home/<USER>/johnny_ws/lapa_ws/LAPA/laq
```

### 3. 训练LAQ模型
```bash
# 查看训练参数
python train_sthv2.py --help

# 示例训练命令（需要提供数据路径）
python train_sthv2.py --data_folder /path/to/your/video/data
```

### 4. 运行推理
```bash
# 查看推理参数
python inference_sthv2.py --help

# 示例推理命令
python inference_sthv2.py \
    --input_file /path/to/input/data \
    --laq_checkpoint /path/to/checkpoint \
    --codebook_size 1024 \
    --window_size 16
```

### 5. 在Python中使用LAQ模块
```python
import sys
sys.path.append('/home/<USER>/johnny_ws/lapa_ws/LAPA/laq')

from laq_model.latent_action_quantization import LatentActionQuantization

# 创建LAQ模型
model = LatentActionQuantization(
    dim=512,
    quant_dim=256,
    codebook_size=1024,
    image_size=224,
    patch_size=16,
    spatial_depth=6,
    temporal_depth=6,
    channels=3
)

# 使用模型进行推理
import torch
video_data = torch.randn(1, 3, 8, 224, 224)  # [batch, channels, frames, height, width]
output = model(video_data)
```

## 验证环境
运行验证脚本确保环境正常工作：
```bash
cd /home/<USER>/johnny_ws/lapa_ws
python test_laq_setup.py
```

## 项目结构
```
LAPA/laq/
├── laq_model/
│   ├── __init__.py
│   ├── latent_action_quantization.py  # 核心LAQ模型
│   ├── nsvq.py                       # 向量量化
│   ├── attention.py                  # 注意力机制
│   ├── data.py                       # 数据处理
│   ├── optimizer.py                  # 优化器
│   └── t5.py                         # T5相关
├── train_sthv2.py                    # 训练脚本
├── inference_sthv2.py                # 推理脚本
└── setup.py                          # 安装配置
```

## 注意事项

1. **数据格式**: LAQ模型期望输入为视频数据，格式为 `[batch, channels, frames, height, width]`

2. **GPU内存**: 模型训练可能需要大量GPU内存，请根据您的硬件调整批次大小

3. **依赖冲突**: 如果遇到依赖冲突，请在`lapa_laq`环境中重新安装相关包

4. **模型检查点**: 训练好的模型会保存为检查点文件，用于后续推理

## 故障排除

### 常见问题
1. **模块导入错误**: 确保在LAQ目录下运行，或将路径添加到Python路径
2. **CUDA内存不足**: 减少批次大小或使用梯度累积
3. **依赖版本冲突**: 重新创建虚拟环境并安装依赖

### 重新安装环境
如果需要重新安装：
```bash
conda remove -n lapa_laq --all
conda create -n lapa_laq python=3.9 -y
conda activate lapa_laq
cd /home/<USER>/johnny_ws/lapa_ws/LAPA/laq
pip install -e . -i https://pypi.tuna.tsinghua.edu.cn/simple
```

## 下一步

现在您可以：
1. 准备视频数据集进行训练
2. 下载预训练的LAQ模型进行推理
3. 根据您的具体需求调整模型参数
4. 集成LAQ到您的视频处理流水线中

环境配置完成！祝您使用愉快！ 🎉
