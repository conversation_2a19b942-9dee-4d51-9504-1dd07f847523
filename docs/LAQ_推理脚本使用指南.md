# LAQ模型推理脚本使用指南

## 概述

LAQ推理脚本是一个完整的解决方案，用于对预处理的帧对数据执行LAQ（Latent Action Quantization）模型推理，生成动作量化码。该脚本支持批量处理、GPU加速、灵活配置和完善的错误处理。

## 功能特性

- ✅ **批量处理**: 支持大规模帧对数据的批量推理
- ✅ **GPU加速**: 自动检测并使用CUDA设备加速推理
- ✅ **灵活配置**: 支持命令行参数和YAML配置文件
- ✅ **错误处理**: 完善的错误处理和重试机制
- ✅ **进度显示**: 实时显示推理进度和统计信息
- ✅ **日志系统**: 详细的日志记录和调试信息
- ✅ **预设配置**: 提供多种预设配置适应不同场景

## 目录结构

```
├── scripts/
│   └── inference/
│       └── laq_inference.py          # 主推理脚本
├── configs/
│   ├── laq_inference.yaml            # 默认配置文件
│   └── presets/                      # 预设配置
│       ├── fast_inference.yaml       # 快速推理配置
│       ├── high_quality.yaml         # 高质量推理配置
│       └── large_scale.yaml          # 大规模推理配置
├── data/
│   └── smart_preprocessed_fixed/     # 预处理数据目录
│       └── laq_dataset_hybrid.jsonl  # 输入数据文件
├── models/
│   └── laq_openx.pt                  # 预训练模型文件
├── results/                          # 推理结果输出目录
└── logs/                            # 日志文件目录
```

## 快速开始

### 1. 环境准备

确保已安装必要的依赖：

```bash
# 安装PyTorch (根据你的CUDA版本选择)
pip install torch torchvision torchaudio

# 安装其他依赖
pip install pillow tqdm pyyaml numpy
```

### 2. 基本使用

最简单的使用方式：

```bash
python scripts/inference/laq_inference.py \
    --input data/smart_preprocessed_fixed/laq_dataset_hybrid.jsonl \
    --output results/laq_results.jsonl
```

### 3. 使用配置文件

推荐使用配置文件进行更灵活的配置：

```bash
python scripts/inference/laq_inference.py --config configs/laq_inference.yaml
```

## 详细使用说明

### 命令行参数

#### 基本参数

- `--config, -c`: 配置文件路径（YAML格式）
- `--input, -i`: 输入JSONL文件路径
- `--output, -o`: 输出JSONL文件路径

#### 模型参数

- `--checkpoint`: 模型检查点文件路径
- `--codebook_size`: 码本大小 (默认: 8)
- `--spatial_depth`: 空间Transformer深度 (默认: 8)
- `--temporal_depth`: 时间Transformer深度 (默认: 8)
- `--code_seq_len`: 代码序列长度 (默认: 4)

#### 推理参数

- `--batch_size`: 批处理大小 (默认: 32)
- `--num_workers`: 数据加载器工作进程数 (默认: 4)

#### 其他参数

- `--log_level`: 日志级别 (DEBUG/INFO/WARNING/ERROR)
- `--log_file`: 日志文件路径

### 配置文件格式

配置文件使用YAML格式，包含以下主要部分：

```yaml
# 模型配置
model:
  dim: 1024
  codebook_size: 8
  checkpoint_path: "models/laq_openx.pt"
  # ... 其他模型参数

# 推理配置
inference:
  batch_size: 32
  num_workers: 4

# 数据配置
data:
  input_file: "data/smart_preprocessed_fixed/laq_dataset_hybrid.jsonl"
  output_file: "results/laq_inference_results.jsonl"

# 日志配置
log_level: "INFO"
log_file: "logs/laq_inference.log"
```

### 预设配置

提供三种预设配置适应不同场景：

#### 1. 快速推理 (`configs/presets/fast_inference.yaml`)

适合测试和快速验证：
- 较小的模型深度 (spatial_depth=4, temporal_depth=4)
- 较大的批处理大小 (batch_size=64)
- 优化推理速度

```bash
python scripts/inference/laq_inference.py --config configs/presets/fast_inference.yaml
```

#### 2. 高质量推理 (`configs/presets/high_quality.yaml`)

适合生产环境：
- 更深的模型 (spatial_depth=12, temporal_depth=12)
- 更大的码本 (codebook_size=1024)
- 优化结果质量

```bash
python scripts/inference/laq_inference.py --config configs/presets/high_quality.yaml
```

#### 3. 大规模推理 (`configs/presets/large_scale.yaml`)

适合处理大型数据集：
- 大批处理大小 (batch_size=128)
- 更多工作进程 (num_workers=8)
- 优化处理效率

```bash
python scripts/inference/laq_inference.py --config configs/presets/large_scale.yaml
```

## 输入数据格式

输入数据应为JSONL格式，每行包含一个帧对的信息：

```json
{
  "id": "video_segment_00001",
  "image": "data/smart_preprocessed_fixed/segment1/keyframe_00015.jpg",
  "next_image": "data/smart_preprocessed_fixed/segment1/keyframe_00023.jpg",
  "instruction": "Predict the action between these two frames",
  "vision": "",
  "fields": "[instruction],[vision],delta",
  "method": "hybrid",
  "frame_indices": [15, 23],
  "frame_interval": 8
}
```

必需字段：
- `id`: 唯一标识符
- `image`: 第一帧图像路径
- `next_image`: 第二帧图像路径
- `instruction`: 指令文本
- `vision`: 视觉描述（可为空）

## 输出数据格式

输出数据为JSONL格式，每行包含推理结果：

```json
{
  "id": "video_segment_00001",
  "image": "data/smart_preprocessed_fixed/segment1/keyframe_00015.jpg",
  "next_image": "data/smart_preprocessed_fixed/segment1/keyframe_00023.jpg",
  "instruction": "Predict the action between these two frames",
  "vision": "",
  "delta": ["3", "7", "1", "5"],
  "fields": "[instruction],[vision],delta"
}
```

输出字段：
- 保留所有输入字段
- `delta`: 动作量化码列表（字符串格式）

## 使用示例

### 示例1: 基本推理

```bash
python scripts/inference/laq_inference.py \
    --input data/smart_preprocessed_fixed/laq_dataset_hybrid.jsonl \
    --output results/basic_results.jsonl \
    --batch_size 32 \
    --log_level INFO
```

### 示例2: 自定义模型参数

```bash
python scripts/inference/laq_inference.py \
    --input data/input.jsonl \
    --output results/custom_results.jsonl \
    --checkpoint models/custom_laq_model.pt \
    --codebook_size 1024 \
    --spatial_depth 12 \
    --temporal_depth 12 \
    --batch_size 16
```

### 示例3: 使用配置文件并覆盖参数

```bash
python scripts/inference/laq_inference.py \
    --config configs/laq_inference.yaml \
    --batch_size 64 \
    --output results/override_results.jsonl
```

### 示例4: 调试模式

```bash
python scripts/inference/laq_inference.py \
    --config configs/presets/fast_inference.yaml \
    --log_level DEBUG \
    --log_file logs/debug_inference.log
```

## 性能优化建议

### GPU内存优化

1. **调整批处理大小**: 根据GPU内存调整`batch_size`
2. **使用混合精度**: 在配置中启用`mixed_precision`（如果支持）
3. **定期清理缓存**: 脚本会自动清理GPU缓存

### 处理速度优化

1. **增加工作进程**: 调整`num_workers`参数
2. **使用SSD存储**: 将数据存储在SSD上
3. **选择合适的预设**: 根据需求选择速度或质量优先的预设

### 大规模数据处理

1. **分批处理**: 将大数据集分成多个小文件
2. **监控资源使用**: 注意内存和磁盘空间使用
3. **使用大规模预设**: 使用`large_scale.yaml`配置

## 故障排除

### 常见问题

1. **CUDA内存不足**
   - 减小`batch_size`
   - 减少`num_workers`
   - 使用较小的模型配置

2. **模型加载失败**
   - 检查模型文件路径
   - 确认模型文件完整性
   - 验证模型参数匹配

3. **图像加载错误**
   - 检查图像文件路径
   - 确认图像文件格式
   - 查看错误日志详情

4. **推理结果异常**
   - 检查输入数据格式
   - 验证模型配置参数
   - 启用DEBUG日志查看详情

### 日志分析

日志文件包含详细的执行信息：

```
2025-08-01 10:00:00 - INFO - 正在加载LAQ模型...
2025-08-01 10:00:05 - INFO - ✓ LAQ模型加载成功，使用设备: cuda
2025-08-01 10:00:05 - INFO - 加载了 1645 个帧对数据
2025-08-01 10:00:10 - INFO - 已处理 100/52 批次，成功样本: 3200，失败批次: 0
```

## 技术支持

如果遇到问题，请：

1. 查看日志文件获取详细错误信息
2. 确认环境配置和依赖安装
3. 验证输入数据格式和模型文件
4. 尝试使用不同的配置参数

---

*最后更新: 2025-08-01*
