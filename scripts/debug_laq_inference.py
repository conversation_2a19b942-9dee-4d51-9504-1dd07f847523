#!/usr/bin/env python3
"""
LAQ推理诊断脚本

该脚本用于诊断LAQ推理过程中的问题，包括：
- 模型加载测试
- 数据格式验证
- 推理流程测试
- 错误定位和修复建议

作者: AI Assistant
日期: 2025-08-01
"""

import os
import sys
import json
import logging
import traceback
from pathlib import Path
import tempfile

import torch
import torch.nn.functional as F
from torchvision import transforms as T
from PIL import Image
import numpy as np

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))
sys.path.append(str(project_root / 'LAPA' / 'laq'))

# 设置详细日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('debug_laq_inference.log')
    ]
)

def test_imports():
    """测试模块导入"""
    print("🔍 测试模块导入...")
    
    try:
        from laq_model import LatentActionQuantization
        print("  ✅ LAQ模型导入成功")
        return LatentActionQuantization
    except ImportError as e:
        print(f"  ❌ LAQ模型导入失败: {e}")
        print(f"  详细错误: {traceback.format_exc()}")
        return None

def test_model_creation(LAQModel):
    """测试模型创建"""
    print("\n🏗️  测试模型创建...")
    
    try:
        # 使用默认配置创建模型
        model = LAQModel(
            dim=1024,
            quant_dim=32,
            codebook_size=8,
            image_size=256,
            patch_size=32,
            spatial_depth=8,
            temporal_depth=8,
            dim_head=64,
            heads=16,
            code_seq_len=4,
        )
        
        print(f"  ✅ 模型创建成功")
        print(f"  - 模型类型: {type(model)}")
        print(f"  - 参数数量: {sum(p.numel() for p in model.parameters()):,}")
        
        return model
    except Exception as e:
        print(f"  ❌ 模型创建失败: {e}")
        print(f"  详细错误: {traceback.format_exc()}")
        return None

def test_model_loading(model, checkpoint_path):
    """测试模型加载"""
    print(f"\n📥 测试模型加载: {checkpoint_path}")
    
    if not os.path.exists(checkpoint_path):
        print(f"  ⚠️  模型文件不存在: {checkpoint_path}")
        return False
    
    try:
        # 检查模型文件
        checkpoint = torch.load(checkpoint_path, map_location='cpu')
        print(f"  ✅ 模型文件加载成功")
        print(f"  - 文件大小: {os.path.getsize(checkpoint_path) / 1024 / 1024:.1f} MB")
        
        if isinstance(checkpoint, dict):
            print(f"  - 检查点键: {list(checkpoint.keys())}")
            if 'model' in checkpoint:
                state_dict = checkpoint['model']
            else:
                state_dict = checkpoint
        else:
            state_dict = checkpoint
        
        print(f"  - 状态字典键数量: {len(state_dict)}")
        
        # 加载到模型
        model.load(checkpoint_path)
        print(f"  ✅ 模型权重加载成功")
        
        return True
    except Exception as e:
        print(f"  ❌ 模型加载失败: {e}")
        print(f"  详细错误: {traceback.format_exc()}")
        return False

def test_model_inference(model, device):
    """测试模型推理"""
    print(f"\n🧠 测试模型推理 (设备: {device})")
    
    try:
        model = model.to(device)
        model.eval()
        
        # 创建测试数据
        batch_size = 2
        test_input = torch.randn(batch_size, 3, 2, 256, 256).to(device)
        
        print(f"  - 测试输入形状: {test_input.shape}")
        print(f"  - 测试输入数据范围: [{test_input.min():.3f}, {test_input.max():.3f}]")
        
        with torch.no_grad():
            # 测试forward方法
            print("  🔄 测试forward方法...")
            output = model(test_input, return_only_codebook_ids=True)
            
            print(f"  ✅ forward推理成功")
            print(f"  - 输出类型: {type(output)}")
            print(f"  - 输出形状: {output.shape}")
            print(f"  - 输出数据类型: {output.dtype}")
            print(f"  - 输出数据范围: [{output.min()}, {output.max()}]")
            
            # 测试inference方法
            print("  🔄 测试inference方法...")
            try:
                output2 = model.inference(test_input, return_only_codebook_ids=True)
                print(f"  ✅ inference推理成功")
                print(f"  - 输出形状: {output2.shape}")
            except Exception as e:
                print(f"  ⚠️  inference方法失败: {e}")
        
        return True
    except Exception as e:
        print(f"  ❌ 模型推理失败: {e}")
        print(f"  详细错误: {traceback.format_exc()}")
        return False

def create_test_data():
    """创建测试数据"""
    print("\n📝 创建测试数据...")
    
    try:
        # 创建临时目录
        test_dir = Path("debug_test_data")
        test_dir.mkdir(exist_ok=True)
        
        # 创建测试图像
        img1 = Image.fromarray(np.random.randint(0, 255, (256, 256, 3), dtype=np.uint8))
        img2 = Image.fromarray(np.random.randint(0, 255, (256, 256, 3), dtype=np.uint8))
        
        img1_path = test_dir / "test_frame1.jpg"
        img2_path = test_dir / "test_frame2.jpg"
        
        img1.save(img1_path)
        img2.save(img2_path)
        
        # 创建测试JSONL文件
        test_data = [
            {
                "id": "debug_test_001",
                "image": str(img1_path),
                "next_image": str(img2_path),
                "instruction": "Predict the action between these two frames",
                "vision": "",
                "fields": "[instruction],[vision],delta"
            }
        ]
        
        test_jsonl = test_dir / "test_input.jsonl"
        with open(test_jsonl, 'w') as f:
            for item in test_data:
                f.write(json.dumps(item) + '\n')
        
        print(f"  ✅ 测试数据创建完成: {test_dir}")
        return test_dir
        
    except Exception as e:
        print(f"  ❌ 创建测试数据失败: {e}")
        return None

def test_data_loading(test_dir):
    """测试数据加载"""
    print("\n📊 测试数据加载...")
    
    try:
        # 导入数据集类
        sys.path.append('scripts/inference')
        from laq_inference import LAQInferenceDataset
        
        # 创建变换
        transform = T.Compose([
            T.Resize(256),
            T.ToTensor(),
        ])
        
        # 创建数据集
        dataset = LAQInferenceDataset(str(test_dir / "test_input.jsonl"), transform)
        
        print(f"  ✅ 数据集创建成功")
        print(f"  - 数据集大小: {len(dataset)}")
        
        # 测试数据加载
        sample = dataset[0]
        frame_pair = sample['frame_pair']
        metadata = sample['metadata']
        
        print(f"  ✅ 数据加载成功")
        print(f"  - 帧对形状: {frame_pair.shape}")
        print(f"  - 帧对数据类型: {frame_pair.dtype}")
        print(f"  - 帧对数据范围: [{frame_pair.min():.3f}, {frame_pair.max():.3f}]")
        print(f"  - 元数据: {metadata['id']}")
        
        return dataset
        
    except Exception as e:
        print(f"  ❌ 数据加载失败: {e}")
        print(f"  详细错误: {traceback.format_exc()}")
        return None

def test_end_to_end_inference(model, dataset, device):
    """测试端到端推理"""
    print(f"\n🔄 测试端到端推理...")
    
    try:
        from torch.utils.data import DataLoader
        
        # 创建数据加载器
        dataloader = DataLoader(dataset, batch_size=1, shuffle=False)
        
        model = model.to(device)
        model.eval()
        
        with torch.no_grad():
            for batch_idx, batch in enumerate(dataloader):
                print(f"  处理批次 {batch_idx}...")
                
                # 获取数据
                frame_pairs = batch['frame_pair'].to(device)
                metadata_list = batch['metadata']
                
                print(f"  - 输入形状: {frame_pairs.shape}")
                print(f"  - 输入设备: {frame_pairs.device}")
                print(f"  - 输入数据范围: [{frame_pairs.min():.3f}, {frame_pairs.max():.3f}]")
                
                # 执行推理
                quantized_codes = model(frame_pairs, return_only_codebook_ids=True)
                
                print(f"  ✅ 推理成功")
                print(f"  - 输出形状: {quantized_codes.shape}")
                print(f"  - 输出内容: {quantized_codes}")
                
                # 处理结果
                for i, codes in enumerate(quantized_codes):
                    result = {
                        'id': metadata_list['id'][i],
                        'delta': [str(code.item()) for code in codes],
                    }
                    print(f"  - 结果 {i}: {result}")
                
                break  # 只测试第一个批次
        
        print(f"  ✅ 端到端推理测试成功")
        return True
        
    except Exception as e:
        print(f"  ❌ 端到端推理失败: {e}")
        print(f"  详细错误: {traceback.format_exc()}")
        return False

def cleanup_test_data():
    """清理测试数据"""
    try:
        import shutil
        test_dir = Path("debug_test_data")
        if test_dir.exists():
            shutil.rmtree(test_dir)
            print("🧹 测试数据清理完成")
    except Exception as e:
        print(f"⚠️  清理测试数据失败: {e}")

def main():
    """主诊断函数"""
    print("🚀 LAQ推理诊断开始\n")
    
    # 检查设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"🖥️  使用设备: {device}")
    if device.type == 'cuda':
        print(f"  - GPU名称: {torch.cuda.get_device_name()}")
        print(f"  - GPU内存: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")
    
    # 测试步骤
    tests = [
        ("模块导入", lambda: test_imports()),
        ("模型创建", lambda: test_model_creation(LAQModel) if 'LAQModel' in globals() else None),
        ("模型加载", lambda: test_model_loading(model, "models/laq_openx.pt") if 'model' in globals() else None),
        ("模型推理", lambda: test_model_inference(model, device) if 'model' in globals() else None),
        ("测试数据创建", lambda: create_test_data()),
        ("数据加载", lambda: test_data_loading(test_dir) if 'test_dir' in globals() else None),
        ("端到端推理", lambda: test_end_to_end_inference(model, dataset, device) if all(x in globals() for x in ['model', 'dataset']) else None),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{'='*60}")
        print(f"测试: {test_name}")
        print('='*60)
        
        try:
            result = test_func()
            if result is not None:
                results[test_name] = result
                # 动态设置全局变量供后续测试使用
                if test_name == "模块导入":
                    globals()['LAQModel'] = result
                elif test_name == "模型创建":
                    globals()['model'] = result
                elif test_name == "测试数据创建":
                    globals()['test_dir'] = result
                elif test_name == "数据加载":
                    globals()['dataset'] = result
                    
                print(f"✅ {test_name} 成功")
            else:
                print(f"❌ {test_name} 失败")
                
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
            print(f"详细错误: {traceback.format_exc()}")
    
    # 清理测试数据
    cleanup_test_data()
    
    # 总结
    print(f"\n{'='*60}")
    print("诊断总结")
    print('='*60)
    
    success_count = sum(1 for result in results.values() if result)
    total_count = len(tests)
    
    print(f"成功测试: {success_count}/{total_count}")
    
    if success_count == total_count:
        print("🎉 所有诊断测试通过！LAQ推理系统正常。")
    else:
        print("❌ 部分测试失败，请检查上述错误信息。")
        
        # 提供修复建议
        print("\n🔧 修复建议:")
        if "模块导入" not in results or not results["模块导入"]:
            print("1. 检查LAPA/laq目录是否存在且包含必要模块")
            print("2. 确认Python路径设置正确")
        
        if "模型加载" not in results or not results["模型加载"]:
            print("3. 检查模型文件models/laq_openx.pt是否存在")
            print("4. 验证模型文件是否完整且未损坏")
        
        if "模型推理" not in results or not results["模型推理"]:
            print("5. 检查GPU内存是否足够")
            print("6. 尝试使用CPU进行推理测试")

if __name__ == '__main__':
    main()
