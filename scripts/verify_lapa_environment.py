#!/usr/bin/env python3
"""
LAPA环境验证脚本
验证LAPA训练环境是否正确配置
"""

import sys
import importlib
from pathlib import Path

def check_import(module_name, description=""):
    """检查模块导入"""
    try:
        module = importlib.import_module(module_name)
        version = getattr(module, '__version__', 'unknown')
        print(f"✅ {module_name} ({version}) - {description}")
        return True
    except ImportError as e:
        print(f"❌ {module_name} - {description}: {e}")
        return False

def check_lapa_modules():
    """检查LAPA特定模块"""
    try:
        from latent_pretraining.delta_llama import VideoLLaMAConfig
        from latent_pretraining.data import DeltaVisionTextProcessor
        from latent_pretraining import train
        print("✅ LAPA核心模块导入成功")
        return True
    except ImportError as e:
        print(f"❌ LAPA核心模块导入失败: {e}")
        return False

def check_cuda_support():
    """检查CUDA支持"""
    try:
        import jax
        devices = jax.devices()
        gpu_devices = [d for d in devices if d.device_kind == 'gpu']
        if gpu_devices:
            print(f"✅ CUDA支持: 发现 {len(gpu_devices)} 个GPU设备")
            for i, device in enumerate(gpu_devices):
                print(f"   GPU {i}: {device}")
        else:
            print("⚠️  未发现GPU设备，将使用CPU训练")
        return True
    except Exception as e:
        print(f"❌ CUDA检查失败: {e}")
        return False

def main():
    print("=== LAPA训练环境验证 ===\n")
    
    success_count = 0
    total_checks = 0
    
    # 检查Python版本
    python_version = f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}"
    print(f"Python版本: {python_version}")
    if sys.version_info >= (3, 8):
        print("✅ Python版本符合要求 (>=3.8)")
        success_count += 1
    else:
        print("❌ Python版本过低，需要>=3.8")
    total_checks += 1
    
    print("\n--- 核心依赖检查 ---")
    
    # 核心框架
    modules_to_check = [
        ("jax", "JAX深度学习框架"),
        ("flax", "Flax神经网络库"),
        ("optax", "JAX优化器库"),
        ("numpy", "数值计算库"),
        ("transformers", "Hugging Face Transformers"),
        ("datasets", "Hugging Face Datasets"),
        ("PIL", "图像处理库"),
        ("tux", "TUX工具库"),
        ("ml_collections", "ML配置管理"),
    ]
    
    for module_name, description in modules_to_check:
        if check_import(module_name, description):
            success_count += 1
        total_checks += 1
    
    print("\n--- LAPA模块检查 ---")
    if check_lapa_modules():
        success_count += 1
    total_checks += 1
    
    print("\n--- 硬件支持检查 ---")
    if check_cuda_support():
        success_count += 1
    total_checks += 1
    
    print("\n--- 路径检查 ---")
    lapa_path = Path("/home/<USER>/johnny_ws/lapa_ws/LAPA")
    if lapa_path.exists():
        print(f"✅ LAPA路径存在: {lapa_path}")
        success_count += 1
    else:
        print(f"❌ LAPA路径不存在: {lapa_path}")
    total_checks += 1
    
    # 检查关键文件
    key_files = [
        "latent_pretraining/train.py",
        "latent_pretraining/delta_llama.py", 
        "latent_pretraining/data.py",
        "requirements.txt"
    ]
    
    for file_path in key_files:
        full_path = lapa_path / file_path
        if full_path.exists():
            print(f"✅ 关键文件存在: {file_path}")
            success_count += 1
        else:
            print(f"❌ 关键文件缺失: {file_path}")
        total_checks += 1
    
    print(f"\n=== 验证结果 ===")
    print(f"通过检查: {success_count}/{total_checks}")
    
    if success_count == total_checks:
        print("🎉 LAPA环境配置完美！可以开始训练。")
        return 0
    elif success_count >= total_checks * 0.8:
        print("⚠️  LAPA环境基本可用，但有一些问题需要注意。")
        return 1
    else:
        print("❌ LAPA环境配置有严重问题，请检查安装。")
        return 2

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
