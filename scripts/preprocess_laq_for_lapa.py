#!/usr/bin/env python3
"""
数据预处理脚本：将LAQ推理结果转换为LAPA官方训练格式
基于LAPA官方数据格式和DeltaVisionTextProcessor规范
"""

import json
import os
import argparse
from pathlib import Path
from typing import Dict, List, Any
import random
from collections import Counter

def load_laq_results(input_file: str) -> List[Dict[str, Any]]:
    """加载LAQ推理结果"""
    results = []
    with open(input_file, 'r', encoding='utf-8') as f:
        for line in f:
            if line.strip():
                results.append(json.loads(line.strip()))
    return results

def validate_delta_tokens(delta_tokens: List[str], vocab_size: int) -> bool:
    """验证delta tokens是否在有效范围内"""
    try:
        for token in delta_tokens:
            token_id = int(token)
            if token_id < 0 or token_id >= vocab_size:
                return False
        return True
    except ValueError:
        return False

def convert_to_lapa_format(laq_data: List[Dict[str, Any]], vocab_size: int) -> List[Dict[str, Any]]:
    """
    将LAQ结果转换为LAPA官方训练格式
    
    LAPA期望的格式：
    {
        "instruction": "指令文本",
        "vision": ["token1", "token2", ...],  # 视觉tokens（如果有）
        "delta": ["token1", "token2", ...],   # delta tokens（LAQ输出）
        "fields": "[instruction],[vision],delta"  # 处理顺序
    }
    """
    lapa_data = []
    invalid_count = 0
    
    for item in laq_data:
        # 验证必要字段
        if not all(key in item for key in ['image', 'next_image', 'delta', 'instruction']):
            invalid_count += 1
            continue
            
        # 验证delta tokens
        if not validate_delta_tokens(item['delta'], vocab_size):
            invalid_count += 1
            continue
        
        # 构造指令文本（模仿LAPA官方格式）
        instruction_text = f"<s> You are a helpful assistant. USER: {item['instruction']} ASSISTANT:"
        
        # 转换为LAPA格式
        lapa_item = {
            'instruction': instruction_text,
            'vision': [],  # 暂时为空，因为我们主要关注delta tokens
            'delta': item['delta'],  # LAQ预测的delta tokens
            'fields': '[instruction],[vision],delta'  # LAPA处理顺序
        }
        
        # 添加原始信息用于调试
        lapa_item['_original_id'] = item.get('id', f"laq_{len(lapa_data)}")
        lapa_item['_image_path'] = item['image']
        lapa_item['_next_image_path'] = item['next_image']
        
        lapa_data.append(lapa_item)
    
    print(f"转换完成: {len(lapa_data)} 条有效数据, {invalid_count} 条无效数据被跳过")
    return lapa_data

def analyze_delta_distribution(data: List[Dict[str, Any]]) -> Dict[str, Any]:
    """分析delta tokens的分布"""
    all_tokens = []
    sequence_lengths = []
    
    for item in data:
        delta_tokens = item['delta']
        all_tokens.extend(delta_tokens)
        sequence_lengths.append(len(delta_tokens))
    
    token_counter = Counter(all_tokens)
    length_counter = Counter(sequence_lengths)
    
    analysis = {
        'total_sequences': len(data),
        'total_tokens': len(all_tokens),
        'unique_tokens': len(token_counter),
        'token_distribution': dict(token_counter.most_common(10)),
        'sequence_length_distribution': dict(length_counter),
        'avg_sequence_length': sum(sequence_lengths) / len(sequence_lengths) if sequence_lengths else 0,
        'max_sequence_length': max(sequence_lengths) if sequence_lengths else 0,
        'min_sequence_length': min(sequence_lengths) if sequence_lengths else 0
    }
    
    return analysis

def save_jsonl(data: List[Dict[str, Any]], output_file: str):
    """保存为JSONL格式"""
    with open(output_file, 'w', encoding='utf-8') as f:
        for item in data:
            f.write(json.dumps(item, ensure_ascii=False) + '\n')

def create_sample_data(data: List[Dict[str, Any]], sample_size: int = 100) -> List[Dict[str, Any]]:
    """创建样本数据用于快速测试"""
    if len(data) <= sample_size:
        return data
    return random.sample(data, sample_size)

def main():
    parser = argparse.ArgumentParser(description='将LAQ推理结果转换为LAPA官方训练格式')
    parser.add_argument('--input_file', required=True, help='LAQ推理结果文件路径')
    parser.add_argument('--output_file', required=True, help='输出LAPA训练文件路径')
    parser.add_argument('--delta_vocab_size', type=int, default=8, help='Delta词汇表大小')
    parser.add_argument('--create_sample', action='store_true', help='创建样本数据用于测试')
    parser.add_argument('--sample_size', type=int, default=100, help='样本数据大小')
    parser.add_argument('--seed', type=int, default=42, help='随机种子')
    
    args = parser.parse_args()
    
    # 设置随机种子
    random.seed(args.seed)
    
    # 创建输出目录
    output_dir = Path(args.output_file).parent
    output_dir.mkdir(parents=True, exist_ok=True)
    
    print(f"开始处理LAQ结果文件: {args.input_file}")
    
    # 加载LAQ结果
    laq_data = load_laq_results(args.input_file)
    print(f"加载了 {len(laq_data)} 条LAQ结果")
    
    # 转换格式
    lapa_data = convert_to_lapa_format(laq_data, args.delta_vocab_size)
    
    if not lapa_data:
        print("错误: 没有有效的数据可以转换")
        return
    
    # 创建样本数据（如果需要）
    if args.create_sample:
        lapa_data = create_sample_data(lapa_data, args.sample_size)
        print(f"创建样本数据: {len(lapa_data)} 条")
    
    # 分析数据分布
    analysis = analyze_delta_distribution(lapa_data)
    print("\n=== 数据分析 ===")
    print(f"总序列数: {analysis['total_sequences']}")
    print(f"总token数: {analysis['total_tokens']}")
    print(f"唯一token数: {analysis['unique_tokens']}")
    print(f"平均序列长度: {analysis['avg_sequence_length']:.2f}")
    print(f"序列长度范围: {analysis['min_sequence_length']} - {analysis['max_sequence_length']}")
    print(f"序列长度分布: {analysis['sequence_length_distribution']}")
    print(f"高频token: {analysis['token_distribution']}")
    
    # 保存分析结果
    analysis_file = output_dir / 'laq_data_analysis.json'
    with open(analysis_file, 'w', encoding='utf-8') as f:
        json.dump(analysis, f, ensure_ascii=False, indent=2)
    
    # 保存LAPA训练数据
    save_jsonl(lapa_data, args.output_file)
    
    print(f"\n=== 处理完成 ===")
    print(f"LAPA训练数据已保存到: {args.output_file}")
    print(f"数据分析报告已保存到: {analysis_file}")
    print(f"总计 {len(lapa_data)} 条训练样本")
    
    # 显示样本数据
    print(f"\n=== 样本数据格式 ===")
    if lapa_data:
        sample = lapa_data[0].copy()
        # 移除调试信息用于显示
        for key in list(sample.keys()):
            if key.startswith('_'):
                del sample[key]
        print(json.dumps(sample, ensure_ascii=False, indent=2))

if __name__ == '__main__':
    main()
