#!/bin/bash

# LAQ模型推理快速启动脚本
# 该脚本提供了多种预设配置的快速启动选项

set -e  # 遇到错误时退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示使用帮助
show_help() {
    echo "LAQ模型推理快速启动脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help              显示此帮助信息"
    echo "  -m, --mode MODE         推理模式 (fast|quality|scale|custom)"
    echo "  -i, --input FILE        输入JSONL文件路径"
    echo "  -o, --output FILE       输出JSONL文件路径"
    echo "  -c, --config FILE       自定义配置文件路径"
    echo "  -b, --batch_size SIZE   批处理大小"
    echo "  -d, --device DEVICE     设备类型 (cuda|cpu)"
    echo "  --dry-run              仅显示将要执行的命令，不实际运行"
    echo ""
    echo "推理模式说明:"
    echo "  fast     - 快速推理模式 (适合测试)"
    echo "  quality  - 高质量推理模式 (适合生产)"
    echo "  scale    - 大规模推理模式 (适合大数据集)"
    echo "  custom   - 自定义配置模式"
    echo ""
    echo "示例:"
    echo "  $0 --mode fast"
    echo "  $0 --mode quality --input data/my_data.jsonl --output results/my_results.jsonl"
    echo "  $0 --mode custom --config configs/my_config.yaml"
    echo "  $0 --input data/input.jsonl --output results/output.jsonl --batch_size 64"
}

# 检查必要的文件和目录
check_prerequisites() {
    print_info "检查环境和文件..."
    
    # 检查Python脚本
    if [ ! -f "scripts/inference/laq_inference.py" ]; then
        print_error "推理脚本不存在: scripts/inference/laq_inference.py"
        exit 1
    fi
    
    # 检查模型文件
    if [ ! -f "models/laq_openx.pt" ]; then
        print_warning "模型文件不存在: models/laq_openx.pt"
        print_warning "请确保模型文件路径正确"
    fi
    
    # 检查默认输入数据
    if [ ! -f "data/smart_preprocessed_fixed/laq_dataset_hybrid.jsonl" ]; then
        print_warning "默认输入数据不存在: data/smart_preprocessed_fixed/laq_dataset_hybrid.jsonl"
    fi
    
    # 创建必要的目录
    mkdir -p results logs
    
    print_success "环境检查完成"
}

# 检查Python依赖
check_python_deps() {
    print_info "检查Python依赖..."
    
    python3 -c "import torch, torchvision, PIL, tqdm, yaml, numpy" 2>/dev/null || {
        print_error "缺少必要的Python依赖"
        print_info "请运行以下命令安装依赖:"
        echo "pip install torch torchvision pillow tqdm pyyaml numpy"
        exit 1
    }
    
    print_success "Python依赖检查完成"
}

# 获取GPU信息
check_gpu() {
    if command -v nvidia-smi &> /dev/null; then
        gpu_count=$(nvidia-smi --query-gpu=count --format=csv,noheader,nounits | head -1)
        gpu_memory=$(nvidia-smi --query-gpu=memory.total --format=csv,noheader,nounits | head -1)
        print_info "检测到 ${gpu_count} 个GPU，显存: ${gpu_memory}MB"
        
        # 根据显存推荐批处理大小
        if [ "$gpu_memory" -lt 8000 ]; then
            recommended_batch_size=16
        elif [ "$gpu_memory" -lt 16000 ]; then
            recommended_batch_size=32
        else
            recommended_batch_size=64
        fi
        print_info "推荐批处理大小: $recommended_batch_size"
    else
        print_warning "未检测到NVIDIA GPU，将使用CPU推理"
    fi
}

# 构建推理命令
build_command() {
    local mode="$1"
    local input_file="$2"
    local output_file="$3"
    local config_file="$4"
    local batch_size="$5"
    local device="$6"
    
    cmd="python3 scripts/inference/laq_inference.py"
    
    case "$mode" in
        "fast")
            cmd="$cmd --config configs/presets/fast_inference.yaml"
            ;;
        "quality")
            cmd="$cmd --config configs/presets/high_quality.yaml"
            ;;
        "scale")
            cmd="$cmd --config configs/presets/large_scale.yaml"
            ;;
        "custom")
            if [ -n "$config_file" ]; then
                cmd="$cmd --config $config_file"
            else
                cmd="$cmd --config configs/laq_inference.yaml"
            fi
            ;;
        *)
            cmd="$cmd --config configs/laq_inference.yaml"
            ;;
    esac
    
    # 添加输入输出文件
    if [ -n "$input_file" ]; then
        cmd="$cmd --input $input_file"
    fi
    
    if [ -n "$output_file" ]; then
        cmd="$cmd --output $output_file"
    fi
    
    # 添加批处理大小
    if [ -n "$batch_size" ]; then
        cmd="$cmd --batch_size $batch_size"
    fi
    
    echo "$cmd"
}

# 主函数
main() {
    local mode=""
    local input_file=""
    local output_file=""
    local config_file=""
    local batch_size=""
    local device=""
    local dry_run=false
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -m|--mode)
                mode="$2"
                shift 2
                ;;
            -i|--input)
                input_file="$2"
                shift 2
                ;;
            -o|--output)
                output_file="$2"
                shift 2
                ;;
            -c|--config)
                config_file="$2"
                shift 2
                ;;
            -b|--batch_size)
                batch_size="$2"
                shift 2
                ;;
            -d|--device)
                device="$2"
                shift 2
                ;;
            --dry-run)
                dry_run=true
                shift
                ;;
            *)
                print_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 默认值
    if [ -z "$mode" ]; then
        mode="fast"
        print_info "使用默认推理模式: fast"
    fi
    
    # 检查环境
    check_prerequisites
    check_python_deps
    check_gpu
    
    # 构建命令
    cmd=$(build_command "$mode" "$input_file" "$output_file" "$config_file" "$batch_size" "$device")
    
    print_info "推理模式: $mode"
    print_info "执行命令: $cmd"
    
    if [ "$dry_run" = true ]; then
        print_warning "干运行模式，不执行实际推理"
        exit 0
    fi
    
    # 执行推理
    print_info "开始执行LAQ模型推理..."
    
    if eval "$cmd"; then
        print_success "LAQ模型推理完成！"
        
        # 显示结果信息
        if [ -n "$output_file" ] && [ -f "$output_file" ]; then
            result_count=$(wc -l < "$output_file")
            print_success "生成了 $result_count 个推理结果"
            print_info "结果文件: $output_file"
        fi
    else
        print_error "LAQ模型推理失败"
        exit 1
    fi
}

# 运行主函数
main "$@"
