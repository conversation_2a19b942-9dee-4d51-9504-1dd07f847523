2025-08-01 14:37:27,635 - INFO - 正在加载LAQ模型...
2025-08-01 14:37:30,345 - INFO - 从 models/laq_openx.pt 加载模型权重...
2025-08-01 14:37:31,341 - INFO - ✓ 模型验证通过，测试输出形状: torch.<PERSON><PERSON>([1, 4])
2025-08-01 14:37:31,341 - INFO - ✓ LAQ模型加载成功，使用设备: cuda
2025-08-01 14:37:31,341 - INFO -   - 模型参数: dim=1024, codebook_size=8
2025-08-01 14:37:31,342 - INFO - ✓ 图像变换设置完成，目标尺寸: 256
2025-08-01 14:37:31,342 - INFO - 开始推理任务...
2025-08-01 14:37:31,342 - INFO - 输入文件: data/smart_preprocessed_fixed/laq_dataset_hybrid.jsonl
2025-08-01 14:37:31,342 - INFO - 输出文件: results/test_laq_results.jsonl
2025-08-01 14:37:31,350 - INFO - 加载了 1644 个帧对数据
2025-08-01 14:37:31,471 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:37:31,471 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:37:31,495 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:37:31,495 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:37:31,502 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:37:31,502 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:37:31,527 - DEBUG - 批次 0 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:31,528 - DEBUG - 批次 0 数据类型: torch.float32
2025-08-01 14:37:31,528 - DEBUG - 批次 0 设备: cuda:0
2025-08-01 14:37:31,540 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:37:31,540 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:37:31,543 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:37:31,549 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:37:31,551 - DEBUG - 批次 0 数据范围: [0.000, 1.000]
2025-08-01 14:37:31,574 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:37:31,574 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:37:31,579 - WARNING - 批次 0 处理失败，重试 1/3
2025-08-01 14:37:31,579 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:31,580 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:37:31,582 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:37:31,606 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:37:31,608 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:37:31,613 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:37:31,618 - DEBUG - 已清理GPU缓存
2025-08-01 14:37:31,637 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:37:31,637 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:37:32,120 - DEBUG - 批次 0 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:32,120 - DEBUG - 批次 0 数据类型: torch.float32
2025-08-01 14:37:32,120 - DEBUG - 批次 0 设备: cuda:0
2025-08-01 14:37:32,121 - DEBUG - 批次 0 数据范围: [0.000, 1.000]
2025-08-01 14:37:32,122 - WARNING - 批次 0 处理失败，重试 2/3
2025-08-01 14:37:32,122 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:32,122 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:37:32,122 - DEBUG - 已清理GPU缓存
2025-08-01 14:37:32,624 - DEBUG - 批次 0 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:32,624 - DEBUG - 批次 0 数据类型: torch.float32
2025-08-01 14:37:32,624 - DEBUG - 批次 0 设备: cuda:0
2025-08-01 14:37:32,625 - DEBUG - 批次 0 数据范围: [0.000, 1.000]
2025-08-01 14:37:32,626 - ERROR - 批次 0 处理失败，已重试 3 次
2025-08-01 14:37:32,626 - ERROR - 错误类型: ValueError
2025-08-01 14:37:32,626 - ERROR - 错误信息: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:32,626 - ERROR - 详细堆栈跟踪:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:37:32,626 - ERROR - 失败批次信息:
2025-08-01 14:37:32,627 - ERROR -   - 批次索引: 0
2025-08-01 14:37:32,627 - ERROR -   - 批次大小: 2
2025-08-01 14:37:32,627 - ERROR -   - 无法获取批次详细信息
2025-08-01 14:37:32,629 - DEBUG - 批次 1 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:32,629 - DEBUG - 批次 1 数据类型: torch.float32
2025-08-01 14:37:32,629 - DEBUG - 批次 1 设备: cuda:0
2025-08-01 14:37:32,630 - DEBUG - 批次 1 数据范围: [0.000, 1.000]
2025-08-01 14:37:32,630 - WARNING - 批次 1 处理失败，重试 1/3
2025-08-01 14:37:32,630 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:32,630 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:37:32,630 - DEBUG - 已清理GPU缓存
2025-08-01 14:37:32,661 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:37:32,688 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:37:33,131 - DEBUG - 批次 1 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:33,132 - DEBUG - 批次 1 数据类型: torch.float32
2025-08-01 14:37:33,132 - DEBUG - 批次 1 设备: cuda:0
2025-08-01 14:37:33,132 - DEBUG - 批次 1 数据范围: [0.000, 1.000]
2025-08-01 14:37:33,133 - WARNING - 批次 1 处理失败，重试 2/3
2025-08-01 14:37:33,133 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:33,133 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:37:33,134 - DEBUG - 已清理GPU缓存
2025-08-01 14:37:33,635 - DEBUG - 批次 1 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:33,635 - DEBUG - 批次 1 数据类型: torch.float32
2025-08-01 14:37:33,635 - DEBUG - 批次 1 设备: cuda:0
2025-08-01 14:37:33,636 - DEBUG - 批次 1 数据范围: [0.000, 1.000]
2025-08-01 14:37:33,637 - ERROR - 批次 1 处理失败，已重试 3 次
2025-08-01 14:37:33,637 - ERROR - 错误类型: ValueError
2025-08-01 14:37:33,637 - ERROR - 错误信息: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:33,637 - ERROR - 详细堆栈跟踪:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:37:33,637 - ERROR - 失败批次信息:
2025-08-01 14:37:33,637 - ERROR -   - 批次索引: 1
2025-08-01 14:37:33,637 - ERROR -   - 批次大小: 2
2025-08-01 14:37:33,637 - ERROR -   - 无法获取批次详细信息
2025-08-01 14:37:33,638 - DEBUG - 批次 2 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:33,638 - DEBUG - 批次 2 数据类型: torch.float32
2025-08-01 14:37:33,638 - DEBUG - 批次 2 设备: cuda:0
2025-08-01 14:37:33,639 - DEBUG - 批次 2 数据范围: [0.000, 0.996]
2025-08-01 14:37:33,639 - WARNING - 批次 2 处理失败，重试 1/3
2025-08-01 14:37:33,639 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:33,639 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:37:33,639 - DEBUG - 已清理GPU缓存
2025-08-01 14:37:33,672 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:37:33,698 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:37:34,140 - DEBUG - 批次 2 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:34,141 - DEBUG - 批次 2 数据类型: torch.float32
2025-08-01 14:37:34,141 - DEBUG - 批次 2 设备: cuda:0
2025-08-01 14:37:34,142 - DEBUG - 批次 2 数据范围: [0.000, 0.996]
2025-08-01 14:37:34,142 - WARNING - 批次 2 处理失败，重试 2/3
2025-08-01 14:37:34,142 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:34,142 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:37:34,143 - DEBUG - 已清理GPU缓存
2025-08-01 14:37:34,644 - DEBUG - 批次 2 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:34,644 - DEBUG - 批次 2 数据类型: torch.float32
2025-08-01 14:37:34,644 - DEBUG - 批次 2 设备: cuda:0
2025-08-01 14:37:34,645 - DEBUG - 批次 2 数据范围: [0.000, 0.996]
2025-08-01 14:37:34,646 - ERROR - 批次 2 处理失败，已重试 3 次
2025-08-01 14:37:34,646 - ERROR - 错误类型: ValueError
2025-08-01 14:37:34,646 - ERROR - 错误信息: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:34,646 - ERROR - 详细堆栈跟踪:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:37:34,646 - ERROR - 失败批次信息:
2025-08-01 14:37:34,646 - ERROR -   - 批次索引: 2
2025-08-01 14:37:34,646 - ERROR -   - 批次大小: 2
2025-08-01 14:37:34,646 - ERROR -   - 无法获取批次详细信息
2025-08-01 14:37:34,647 - DEBUG - 批次 3 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:34,647 - DEBUG - 批次 3 数据类型: torch.float32
2025-08-01 14:37:34,647 - DEBUG - 批次 3 设备: cuda:0
2025-08-01 14:37:34,647 - DEBUG - 批次 3 数据范围: [0.000, 0.992]
2025-08-01 14:37:34,648 - WARNING - 批次 3 处理失败，重试 1/3
2025-08-01 14:37:34,648 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:34,648 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:37:34,648 - DEBUG - 已清理GPU缓存
2025-08-01 14:37:34,679 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:37:34,705 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:37:35,149 - DEBUG - 批次 3 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:35,150 - DEBUG - 批次 3 数据类型: torch.float32
2025-08-01 14:37:35,150 - DEBUG - 批次 3 设备: cuda:0
2025-08-01 14:37:35,150 - DEBUG - 批次 3 数据范围: [0.000, 0.992]
2025-08-01 14:37:35,151 - WARNING - 批次 3 处理失败，重试 2/3
2025-08-01 14:37:35,151 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:35,152 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:37:35,152 - DEBUG - 已清理GPU缓存
2025-08-01 14:37:35,653 - DEBUG - 批次 3 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:35,654 - DEBUG - 批次 3 数据类型: torch.float32
2025-08-01 14:37:35,654 - DEBUG - 批次 3 设备: cuda:0
2025-08-01 14:37:35,654 - DEBUG - 批次 3 数据范围: [0.000, 0.992]
2025-08-01 14:37:35,655 - ERROR - 批次 3 处理失败，已重试 3 次
2025-08-01 14:37:35,655 - ERROR - 错误类型: ValueError
2025-08-01 14:37:35,655 - ERROR - 错误信息: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:35,656 - ERROR - 详细堆栈跟踪:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:37:35,656 - ERROR - 失败批次信息:
2025-08-01 14:37:35,656 - ERROR -   - 批次索引: 3
2025-08-01 14:37:35,656 - ERROR -   - 批次大小: 2
2025-08-01 14:37:35,656 - ERROR -   - 无法获取批次详细信息
2025-08-01 14:37:35,658 - DEBUG - 批次 4 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:35,658 - DEBUG - 批次 4 数据类型: torch.float32
2025-08-01 14:37:35,658 - DEBUG - 批次 4 设备: cuda:0
2025-08-01 14:37:35,658 - DEBUG - 批次 4 数据范围: [0.000, 0.996]
2025-08-01 14:37:35,659 - WARNING - 批次 4 处理失败，重试 1/3
2025-08-01 14:37:35,659 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:35,660 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:37:35,660 - DEBUG - 已清理GPU缓存
2025-08-01 14:37:35,687 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:37:35,712 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:37:36,161 - DEBUG - 批次 4 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:36,162 - DEBUG - 批次 4 数据类型: torch.float32
2025-08-01 14:37:36,162 - DEBUG - 批次 4 设备: cuda:0
2025-08-01 14:37:36,162 - DEBUG - 批次 4 数据范围: [0.000, 0.996]
2025-08-01 14:37:36,163 - WARNING - 批次 4 处理失败，重试 2/3
2025-08-01 14:37:36,163 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:36,164 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:37:36,164 - DEBUG - 已清理GPU缓存
2025-08-01 14:37:36,665 - DEBUG - 批次 4 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:36,666 - DEBUG - 批次 4 数据类型: torch.float32
2025-08-01 14:37:36,666 - DEBUG - 批次 4 设备: cuda:0
2025-08-01 14:37:36,666 - DEBUG - 批次 4 数据范围: [0.000, 0.996]
2025-08-01 14:37:36,667 - ERROR - 批次 4 处理失败，已重试 3 次
2025-08-01 14:37:36,667 - ERROR - 错误类型: ValueError
2025-08-01 14:37:36,668 - ERROR - 错误信息: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:36,668 - ERROR - 详细堆栈跟踪:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:37:36,668 - ERROR - 失败批次信息:
2025-08-01 14:37:36,668 - ERROR -   - 批次索引: 4
2025-08-01 14:37:36,668 - ERROR -   - 批次大小: 2
2025-08-01 14:37:36,668 - ERROR -   - 无法获取批次详细信息
2025-08-01 14:37:36,670 - DEBUG - 批次 5 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:36,670 - DEBUG - 批次 5 数据类型: torch.float32
2025-08-01 14:37:36,670 - DEBUG - 批次 5 设备: cuda:0
2025-08-01 14:37:36,671 - DEBUG - 批次 5 数据范围: [0.000, 0.996]
2025-08-01 14:37:36,671 - WARNING - 批次 5 处理失败，重试 1/3
2025-08-01 14:37:36,671 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:36,672 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:37:36,672 - DEBUG - 已清理GPU缓存
2025-08-01 14:37:36,699 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:37:36,727 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:37:37,173 - DEBUG - 批次 5 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:37,174 - DEBUG - 批次 5 数据类型: torch.float32
2025-08-01 14:37:37,174 - DEBUG - 批次 5 设备: cuda:0
2025-08-01 14:37:37,174 - DEBUG - 批次 5 数据范围: [0.000, 0.996]
2025-08-01 14:37:37,175 - WARNING - 批次 5 处理失败，重试 2/3
2025-08-01 14:37:37,176 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:37,176 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:37:37,176 - DEBUG - 已清理GPU缓存
2025-08-01 14:37:37,677 - DEBUG - 批次 5 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:37,678 - DEBUG - 批次 5 数据类型: torch.float32
2025-08-01 14:37:37,678 - DEBUG - 批次 5 设备: cuda:0
2025-08-01 14:37:37,678 - DEBUG - 批次 5 数据范围: [0.000, 0.996]
2025-08-01 14:37:37,679 - ERROR - 批次 5 处理失败，已重试 3 次
2025-08-01 14:37:37,679 - ERROR - 错误类型: ValueError
2025-08-01 14:37:37,680 - ERROR - 错误信息: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:37,680 - ERROR - 详细堆栈跟踪:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:37:37,680 - ERROR - 失败批次信息:
2025-08-01 14:37:37,680 - ERROR -   - 批次索引: 5
2025-08-01 14:37:37,680 - ERROR -   - 批次大小: 2
2025-08-01 14:37:37,680 - ERROR -   - 无法获取批次详细信息
2025-08-01 14:37:37,682 - DEBUG - 批次 6 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:37,682 - DEBUG - 批次 6 数据类型: torch.float32
2025-08-01 14:37:37,682 - DEBUG - 批次 6 设备: cuda:0
2025-08-01 14:37:37,683 - DEBUG - 批次 6 数据范围: [0.000, 0.996]
2025-08-01 14:37:37,683 - WARNING - 批次 6 处理失败，重试 1/3
2025-08-01 14:37:37,683 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:37,683 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:37:37,683 - DEBUG - 已清理GPU缓存
2025-08-01 14:37:37,713 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:37:37,739 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:37:38,185 - DEBUG - 批次 6 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:38,185 - DEBUG - 批次 6 数据类型: torch.float32
2025-08-01 14:37:38,185 - DEBUG - 批次 6 设备: cuda:0
2025-08-01 14:37:38,186 - DEBUG - 批次 6 数据范围: [0.000, 0.996]
2025-08-01 14:37:38,187 - WARNING - 批次 6 处理失败，重试 2/3
2025-08-01 14:37:38,187 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:38,187 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:37:38,187 - DEBUG - 已清理GPU缓存
2025-08-01 14:37:38,689 - DEBUG - 批次 6 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:38,689 - DEBUG - 批次 6 数据类型: torch.float32
2025-08-01 14:37:38,689 - DEBUG - 批次 6 设备: cuda:0
2025-08-01 14:37:38,690 - DEBUG - 批次 6 数据范围: [0.000, 0.996]
2025-08-01 14:37:38,690 - ERROR - 批次 6 处理失败，已重试 3 次
2025-08-01 14:37:38,691 - ERROR - 错误类型: ValueError
2025-08-01 14:37:38,691 - ERROR - 错误信息: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:38,691 - ERROR - 详细堆栈跟踪:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:37:38,691 - ERROR - 失败批次信息:
2025-08-01 14:37:38,691 - ERROR -   - 批次索引: 6
2025-08-01 14:37:38,691 - ERROR -   - 批次大小: 2
2025-08-01 14:37:38,691 - ERROR -   - 无法获取批次详细信息
2025-08-01 14:37:38,693 - DEBUG - 批次 7 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:38,693 - DEBUG - 批次 7 数据类型: torch.float32
2025-08-01 14:37:38,693 - DEBUG - 批次 7 设备: cuda:0
2025-08-01 14:37:38,693 - DEBUG - 批次 7 数据范围: [0.000, 0.996]
2025-08-01 14:37:38,693 - WARNING - 批次 7 处理失败，重试 1/3
2025-08-01 14:37:38,693 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:38,693 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:37:38,693 - DEBUG - 已清理GPU缓存
2025-08-01 14:37:38,725 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:37:38,752 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:37:39,195 - DEBUG - 批次 7 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:39,195 - DEBUG - 批次 7 数据类型: torch.float32
2025-08-01 14:37:39,195 - DEBUG - 批次 7 设备: cuda:0
2025-08-01 14:37:39,196 - DEBUG - 批次 7 数据范围: [0.000, 0.996]
2025-08-01 14:37:39,196 - WARNING - 批次 7 处理失败，重试 2/3
2025-08-01 14:37:39,196 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:39,196 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:37:39,197 - DEBUG - 已清理GPU缓存
2025-08-01 14:37:39,698 - DEBUG - 批次 7 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:39,698 - DEBUG - 批次 7 数据类型: torch.float32
2025-08-01 14:37:39,699 - DEBUG - 批次 7 设备: cuda:0
2025-08-01 14:37:39,699 - DEBUG - 批次 7 数据范围: [0.000, 0.996]
2025-08-01 14:37:39,700 - ERROR - 批次 7 处理失败，已重试 3 次
2025-08-01 14:37:39,700 - ERROR - 错误类型: ValueError
2025-08-01 14:37:39,700 - ERROR - 错误信息: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:39,700 - ERROR - 详细堆栈跟踪:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:37:39,701 - ERROR - 失败批次信息:
2025-08-01 14:37:39,701 - ERROR -   - 批次索引: 7
2025-08-01 14:37:39,701 - ERROR -   - 批次大小: 2
2025-08-01 14:37:39,701 - ERROR -   - 无法获取批次详细信息
2025-08-01 14:37:39,702 - DEBUG - 批次 8 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:39,702 - DEBUG - 批次 8 数据类型: torch.float32
2025-08-01 14:37:39,702 - DEBUG - 批次 8 设备: cuda:0
2025-08-01 14:37:39,702 - DEBUG - 批次 8 数据范围: [0.000, 0.996]
2025-08-01 14:37:39,702 - WARNING - 批次 8 处理失败，重试 1/3
2025-08-01 14:37:39,702 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:39,702 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:37:39,702 - DEBUG - 已清理GPU缓存
2025-08-01 14:37:39,733 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:37:39,760 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:37:40,204 - DEBUG - 批次 8 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:40,204 - DEBUG - 批次 8 数据类型: torch.float32
2025-08-01 14:37:40,204 - DEBUG - 批次 8 设备: cuda:0
2025-08-01 14:37:40,205 - DEBUG - 批次 8 数据范围: [0.000, 0.996]
2025-08-01 14:37:40,206 - WARNING - 批次 8 处理失败，重试 2/3
2025-08-01 14:37:40,206 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:40,206 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:37:40,206 - DEBUG - 已清理GPU缓存
2025-08-01 14:37:40,707 - DEBUG - 批次 8 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:40,708 - DEBUG - 批次 8 数据类型: torch.float32
2025-08-01 14:37:40,708 - DEBUG - 批次 8 设备: cuda:0
2025-08-01 14:37:40,708 - DEBUG - 批次 8 数据范围: [0.000, 0.996]
2025-08-01 14:37:40,709 - ERROR - 批次 8 处理失败，已重试 3 次
2025-08-01 14:37:40,709 - ERROR - 错误类型: ValueError
2025-08-01 14:37:40,709 - ERROR - 错误信息: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:40,710 - ERROR - 详细堆栈跟踪:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:37:40,710 - ERROR - 失败批次信息:
2025-08-01 14:37:40,710 - ERROR -   - 批次索引: 8
2025-08-01 14:37:40,710 - ERROR -   - 批次大小: 2
2025-08-01 14:37:40,710 - ERROR -   - 无法获取批次详细信息
2025-08-01 14:37:40,711 - DEBUG - 批次 9 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:40,711 - DEBUG - 批次 9 数据类型: torch.float32
2025-08-01 14:37:40,711 - DEBUG - 批次 9 设备: cuda:0
2025-08-01 14:37:40,711 - DEBUG - 批次 9 数据范围: [0.000, 0.992]
2025-08-01 14:37:40,712 - WARNING - 批次 9 处理失败，重试 1/3
2025-08-01 14:37:40,712 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:40,712 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:37:40,712 - DEBUG - 已清理GPU缓存
2025-08-01 14:37:40,744 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:37:40,769 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:37:41,213 - DEBUG - 批次 9 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:41,214 - DEBUG - 批次 9 数据类型: torch.float32
2025-08-01 14:37:41,214 - DEBUG - 批次 9 设备: cuda:0
2025-08-01 14:37:41,214 - DEBUG - 批次 9 数据范围: [0.000, 0.992]
2025-08-01 14:37:41,215 - WARNING - 批次 9 处理失败，重试 2/3
2025-08-01 14:37:41,215 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:41,215 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:37:41,215 - DEBUG - 已清理GPU缓存
2025-08-01 14:37:41,717 - DEBUG - 批次 9 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:41,717 - DEBUG - 批次 9 数据类型: torch.float32
2025-08-01 14:37:41,717 - DEBUG - 批次 9 设备: cuda:0
2025-08-01 14:37:41,718 - DEBUG - 批次 9 数据范围: [0.000, 0.992]
2025-08-01 14:37:41,719 - ERROR - 批次 9 处理失败，已重试 3 次
2025-08-01 14:37:41,720 - ERROR - 错误类型: ValueError
2025-08-01 14:37:41,720 - ERROR - 错误信息: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:41,721 - ERROR - 详细堆栈跟踪:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:37:41,721 - ERROR - 失败批次信息:
2025-08-01 14:37:41,721 - ERROR -   - 批次索引: 9
2025-08-01 14:37:41,721 - ERROR -   - 批次大小: 2
2025-08-01 14:37:41,721 - ERROR -   - 无法获取批次详细信息
2025-08-01 14:37:41,722 - DEBUG - 批次 10 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:41,722 - DEBUG - 批次 10 数据类型: torch.float32
2025-08-01 14:37:41,722 - DEBUG - 批次 10 设备: cuda:0
2025-08-01 14:37:41,722 - DEBUG - 批次 10 数据范围: [0.000, 0.992]
2025-08-01 14:37:41,722 - WARNING - 批次 10 处理失败，重试 1/3
2025-08-01 14:37:41,723 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:41,723 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:37:41,723 - DEBUG - 已清理GPU缓存
2025-08-01 14:37:41,756 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:37:41,785 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:37:42,224 - DEBUG - 批次 10 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:42,224 - DEBUG - 批次 10 数据类型: torch.float32
2025-08-01 14:37:42,225 - DEBUG - 批次 10 设备: cuda:0
2025-08-01 14:37:42,225 - DEBUG - 批次 10 数据范围: [0.000, 0.992]
2025-08-01 14:37:42,226 - WARNING - 批次 10 处理失败，重试 2/3
2025-08-01 14:37:42,226 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:42,226 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:37:42,227 - DEBUG - 已清理GPU缓存
2025-08-01 14:37:42,728 - DEBUG - 批次 10 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:42,728 - DEBUG - 批次 10 数据类型: torch.float32
2025-08-01 14:37:42,729 - DEBUG - 批次 10 设备: cuda:0
2025-08-01 14:37:42,729 - DEBUG - 批次 10 数据范围: [0.000, 0.992]
2025-08-01 14:37:42,730 - ERROR - 批次 10 处理失败，已重试 3 次
2025-08-01 14:37:42,730 - ERROR - 错误类型: ValueError
2025-08-01 14:37:42,730 - ERROR - 错误信息: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:42,730 - ERROR - 详细堆栈跟踪:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:37:42,731 - ERROR - 失败批次信息:
2025-08-01 14:37:42,731 - ERROR -   - 批次索引: 10
2025-08-01 14:37:42,731 - ERROR -   - 批次大小: 2
2025-08-01 14:37:42,731 - ERROR -   - 无法获取批次详细信息
2025-08-01 14:37:42,733 - DEBUG - 批次 11 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:42,733 - DEBUG - 批次 11 数据类型: torch.float32
2025-08-01 14:37:42,733 - DEBUG - 批次 11 设备: cuda:0
2025-08-01 14:37:42,734 - DEBUG - 批次 11 数据范围: [0.000, 0.996]
2025-08-01 14:37:42,734 - WARNING - 批次 11 处理失败，重试 1/3
2025-08-01 14:37:42,734 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:42,735 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:37:42,735 - DEBUG - 已清理GPU缓存
2025-08-01 14:37:42,763 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:37:42,791 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:37:43,236 - DEBUG - 批次 11 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:43,237 - DEBUG - 批次 11 数据类型: torch.float32
2025-08-01 14:37:43,237 - DEBUG - 批次 11 设备: cuda:0
2025-08-01 14:37:43,237 - DEBUG - 批次 11 数据范围: [0.000, 0.996]
2025-08-01 14:37:43,238 - WARNING - 批次 11 处理失败，重试 2/3
2025-08-01 14:37:43,238 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:43,239 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:37:43,239 - DEBUG - 已清理GPU缓存
2025-08-01 14:37:43,740 - DEBUG - 批次 11 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:43,741 - DEBUG - 批次 11 数据类型: torch.float32
2025-08-01 14:37:43,741 - DEBUG - 批次 11 设备: cuda:0
2025-08-01 14:37:43,741 - DEBUG - 批次 11 数据范围: [0.000, 0.996]
2025-08-01 14:37:43,742 - ERROR - 批次 11 处理失败，已重试 3 次
2025-08-01 14:37:43,742 - ERROR - 错误类型: ValueError
2025-08-01 14:37:43,743 - ERROR - 错误信息: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:43,743 - ERROR - 详细堆栈跟踪:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:37:43,743 - ERROR - 失败批次信息:
2025-08-01 14:37:43,743 - ERROR -   - 批次索引: 11
2025-08-01 14:37:43,743 - ERROR -   - 批次大小: 2
2025-08-01 14:37:43,743 - ERROR -   - 无法获取批次详细信息
2025-08-01 14:37:43,745 - DEBUG - 批次 12 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:43,745 - DEBUG - 批次 12 数据类型: torch.float32
2025-08-01 14:37:43,745 - DEBUG - 批次 12 设备: cuda:0
2025-08-01 14:37:43,746 - DEBUG - 批次 12 数据范围: [0.000, 0.996]
2025-08-01 14:37:43,746 - WARNING - 批次 12 处理失败，重试 1/3
2025-08-01 14:37:43,746 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:43,747 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:37:43,747 - DEBUG - 已清理GPU缓存
2025-08-01 14:37:43,772 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:37:43,798 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:37:44,248 - DEBUG - 批次 12 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:44,249 - DEBUG - 批次 12 数据类型: torch.float32
2025-08-01 14:37:44,249 - DEBUG - 批次 12 设备: cuda:0
2025-08-01 14:37:44,250 - DEBUG - 批次 12 数据范围: [0.000, 0.996]
2025-08-01 14:37:44,250 - WARNING - 批次 12 处理失败，重试 2/3
2025-08-01 14:37:44,251 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:44,251 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:37:44,251 - DEBUG - 已清理GPU缓存
2025-08-01 14:37:44,752 - DEBUG - 批次 12 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:44,753 - DEBUG - 批次 12 数据类型: torch.float32
2025-08-01 14:37:44,753 - DEBUG - 批次 12 设备: cuda:0
2025-08-01 14:37:44,754 - DEBUG - 批次 12 数据范围: [0.000, 0.996]
2025-08-01 14:37:44,754 - ERROR - 批次 12 处理失败，已重试 3 次
2025-08-01 14:37:44,755 - ERROR - 错误类型: ValueError
2025-08-01 14:37:44,755 - ERROR - 错误信息: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:44,755 - ERROR - 详细堆栈跟踪:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:37:44,755 - ERROR - 失败批次信息:
2025-08-01 14:37:44,755 - ERROR -   - 批次索引: 12
2025-08-01 14:37:44,755 - ERROR -   - 批次大小: 2
2025-08-01 14:37:44,756 - ERROR -   - 无法获取批次详细信息
2025-08-01 14:37:44,757 - DEBUG - 批次 13 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:44,757 - DEBUG - 批次 13 数据类型: torch.float32
2025-08-01 14:37:44,757 - DEBUG - 批次 13 设备: cuda:0
2025-08-01 14:37:44,757 - DEBUG - 批次 13 数据范围: [0.000, 0.996]
2025-08-01 14:37:44,757 - WARNING - 批次 13 处理失败，重试 1/3
2025-08-01 14:37:44,758 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:44,758 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:37:44,758 - DEBUG - 已清理GPU缓存
2025-08-01 14:37:44,789 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:37:44,815 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:37:45,259 - DEBUG - 批次 13 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:45,259 - DEBUG - 批次 13 数据类型: torch.float32
2025-08-01 14:37:45,260 - DEBUG - 批次 13 设备: cuda:0
2025-08-01 14:37:45,260 - DEBUG - 批次 13 数据范围: [0.000, 0.996]
2025-08-01 14:37:45,261 - WARNING - 批次 13 处理失败，重试 2/3
2025-08-01 14:37:45,261 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:45,261 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:37:45,261 - DEBUG - 已清理GPU缓存
2025-08-01 14:37:45,763 - DEBUG - 批次 13 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:45,763 - DEBUG - 批次 13 数据类型: torch.float32
2025-08-01 14:37:45,763 - DEBUG - 批次 13 设备: cuda:0
2025-08-01 14:37:45,764 - DEBUG - 批次 13 数据范围: [0.000, 0.996]
2025-08-01 14:37:45,765 - ERROR - 批次 13 处理失败，已重试 3 次
2025-08-01 14:37:45,765 - ERROR - 错误类型: ValueError
2025-08-01 14:37:45,765 - ERROR - 错误信息: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:45,765 - ERROR - 详细堆栈跟踪:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:37:45,765 - ERROR - 失败批次信息:
2025-08-01 14:37:45,766 - ERROR -   - 批次索引: 13
2025-08-01 14:37:45,766 - ERROR -   - 批次大小: 2
2025-08-01 14:37:45,766 - ERROR -   - 无法获取批次详细信息
2025-08-01 14:37:45,767 - DEBUG - 批次 14 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:45,767 - DEBUG - 批次 14 数据类型: torch.float32
2025-08-01 14:37:45,767 - DEBUG - 批次 14 设备: cuda:0
2025-08-01 14:37:45,768 - DEBUG - 批次 14 数据范围: [0.000, 0.996]
2025-08-01 14:37:45,768 - WARNING - 批次 14 处理失败，重试 1/3
2025-08-01 14:37:45,768 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:45,768 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:37:45,768 - DEBUG - 已清理GPU缓存
2025-08-01 14:37:45,799 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:37:45,824 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:37:46,269 - DEBUG - 批次 14 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:46,270 - DEBUG - 批次 14 数据类型: torch.float32
2025-08-01 14:37:46,270 - DEBUG - 批次 14 设备: cuda:0
2025-08-01 14:37:46,271 - DEBUG - 批次 14 数据范围: [0.000, 0.996]
2025-08-01 14:37:46,271 - WARNING - 批次 14 处理失败，重试 2/3
2025-08-01 14:37:46,271 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:46,271 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:37:46,271 - DEBUG - 已清理GPU缓存
2025-08-01 14:37:46,773 - DEBUG - 批次 14 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:46,773 - DEBUG - 批次 14 数据类型: torch.float32
2025-08-01 14:37:46,774 - DEBUG - 批次 14 设备: cuda:0
2025-08-01 14:37:46,774 - DEBUG - 批次 14 数据范围: [0.000, 0.996]
2025-08-01 14:37:46,775 - ERROR - 批次 14 处理失败，已重试 3 次
2025-08-01 14:37:46,775 - ERROR - 错误类型: ValueError
2025-08-01 14:37:46,775 - ERROR - 错误信息: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:46,775 - ERROR - 详细堆栈跟踪:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:37:46,775 - ERROR - 失败批次信息:
2025-08-01 14:37:46,775 - ERROR -   - 批次索引: 14
2025-08-01 14:37:46,775 - ERROR -   - 批次大小: 2
2025-08-01 14:37:46,775 - ERROR -   - 无法获取批次详细信息
2025-08-01 14:37:46,776 - DEBUG - 批次 15 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:46,777 - DEBUG - 批次 15 数据类型: torch.float32
2025-08-01 14:37:46,777 - DEBUG - 批次 15 设备: cuda:0
2025-08-01 14:37:46,777 - DEBUG - 批次 15 数据范围: [0.000, 0.996]
2025-08-01 14:37:46,777 - WARNING - 批次 15 处理失败，重试 1/3
2025-08-01 14:37:46,777 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:46,777 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:37:46,777 - DEBUG - 已清理GPU缓存
2025-08-01 14:37:46,807 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:37:46,833 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:37:47,279 - DEBUG - 批次 15 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:47,279 - DEBUG - 批次 15 数据类型: torch.float32
2025-08-01 14:37:47,279 - DEBUG - 批次 15 设备: cuda:0
2025-08-01 14:37:47,280 - DEBUG - 批次 15 数据范围: [0.000, 0.996]
2025-08-01 14:37:47,281 - WARNING - 批次 15 处理失败，重试 2/3
2025-08-01 14:37:47,281 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:47,281 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:37:47,281 - DEBUG - 已清理GPU缓存
2025-08-01 14:37:47,783 - DEBUG - 批次 15 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:47,783 - DEBUG - 批次 15 数据类型: torch.float32
2025-08-01 14:37:47,783 - DEBUG - 批次 15 设备: cuda:0
2025-08-01 14:37:47,784 - DEBUG - 批次 15 数据范围: [0.000, 0.996]
2025-08-01 14:37:47,785 - ERROR - 批次 15 处理失败，已重试 3 次
2025-08-01 14:37:47,785 - ERROR - 错误类型: ValueError
2025-08-01 14:37:47,785 - ERROR - 错误信息: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:47,785 - ERROR - 详细堆栈跟踪:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:37:47,785 - ERROR - 失败批次信息:
2025-08-01 14:37:47,785 - ERROR -   - 批次索引: 15
2025-08-01 14:37:47,785 - ERROR -   - 批次大小: 2
2025-08-01 14:37:47,785 - ERROR -   - 无法获取批次详细信息
2025-08-01 14:37:47,787 - DEBUG - 批次 16 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:47,787 - DEBUG - 批次 16 数据类型: torch.float32
2025-08-01 14:37:47,787 - DEBUG - 批次 16 设备: cuda:0
2025-08-01 14:37:47,787 - DEBUG - 批次 16 数据范围: [0.000, 1.000]
2025-08-01 14:37:47,788 - WARNING - 批次 16 处理失败，重试 1/3
2025-08-01 14:37:47,788 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:47,788 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:37:47,788 - DEBUG - 已清理GPU缓存
2025-08-01 14:37:47,819 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:37:47,845 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:37:48,290 - DEBUG - 批次 16 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:48,290 - DEBUG - 批次 16 数据类型: torch.float32
2025-08-01 14:37:48,290 - DEBUG - 批次 16 设备: cuda:0
2025-08-01 14:37:48,291 - DEBUG - 批次 16 数据范围: [0.000, 1.000]
2025-08-01 14:37:48,292 - WARNING - 批次 16 处理失败，重试 2/3
2025-08-01 14:37:48,292 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:48,292 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:37:48,292 - DEBUG - 已清理GPU缓存
2025-08-01 14:37:48,794 - DEBUG - 批次 16 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:48,794 - DEBUG - 批次 16 数据类型: torch.float32
2025-08-01 14:37:48,794 - DEBUG - 批次 16 设备: cuda:0
2025-08-01 14:37:48,795 - DEBUG - 批次 16 数据范围: [0.000, 1.000]
2025-08-01 14:37:48,796 - ERROR - 批次 16 处理失败，已重试 3 次
2025-08-01 14:37:48,796 - ERROR - 错误类型: ValueError
2025-08-01 14:37:48,796 - ERROR - 错误信息: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:48,796 - ERROR - 详细堆栈跟踪:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:37:48,796 - ERROR - 失败批次信息:
2025-08-01 14:37:48,797 - ERROR -   - 批次索引: 16
2025-08-01 14:37:48,797 - ERROR -   - 批次大小: 2
2025-08-01 14:37:48,797 - ERROR -   - 无法获取批次详细信息
2025-08-01 14:37:48,798 - DEBUG - 批次 17 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:48,799 - DEBUG - 批次 17 数据类型: torch.float32
2025-08-01 14:37:48,799 - DEBUG - 批次 17 设备: cuda:0
2025-08-01 14:37:48,799 - DEBUG - 批次 17 数据范围: [0.000, 1.000]
2025-08-01 14:37:48,800 - WARNING - 批次 17 处理失败，重试 1/3
2025-08-01 14:37:48,800 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:48,800 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:37:48,800 - DEBUG - 已清理GPU缓存
2025-08-01 14:37:48,828 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:37:48,853 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:37:49,302 - DEBUG - 批次 17 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:49,302 - DEBUG - 批次 17 数据类型: torch.float32
2025-08-01 14:37:49,302 - DEBUG - 批次 17 设备: cuda:0
2025-08-01 14:37:49,303 - DEBUG - 批次 17 数据范围: [0.000, 1.000]
2025-08-01 14:37:49,304 - WARNING - 批次 17 处理失败，重试 2/3
2025-08-01 14:37:49,304 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:49,304 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:37:49,304 - DEBUG - 已清理GPU缓存
2025-08-01 14:37:49,806 - DEBUG - 批次 17 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:49,806 - DEBUG - 批次 17 数据类型: torch.float32
2025-08-01 14:37:49,807 - DEBUG - 批次 17 设备: cuda:0
2025-08-01 14:37:49,807 - DEBUG - 批次 17 数据范围: [0.000, 1.000]
2025-08-01 14:37:49,808 - ERROR - 批次 17 处理失败，已重试 3 次
2025-08-01 14:37:49,808 - ERROR - 错误类型: ValueError
2025-08-01 14:37:49,808 - ERROR - 错误信息: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:49,808 - ERROR - 详细堆栈跟踪:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:37:49,808 - ERROR - 失败批次信息:
2025-08-01 14:37:49,809 - ERROR -   - 批次索引: 17
2025-08-01 14:37:49,809 - ERROR -   - 批次大小: 2
2025-08-01 14:37:49,809 - ERROR -   - 无法获取批次详细信息
2025-08-01 14:37:49,810 - DEBUG - 批次 18 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:49,811 - DEBUG - 批次 18 数据类型: torch.float32
2025-08-01 14:37:49,811 - DEBUG - 批次 18 设备: cuda:0
2025-08-01 14:37:49,811 - DEBUG - 批次 18 数据范围: [0.000, 1.000]
2025-08-01 14:37:49,811 - WARNING - 批次 18 处理失败，重试 1/3
2025-08-01 14:37:49,812 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:49,812 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:37:49,812 - DEBUG - 已清理GPU缓存
2025-08-01 14:37:49,844 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:37:49,870 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:37:50,313 - DEBUG - 批次 18 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:50,313 - DEBUG - 批次 18 数据类型: torch.float32
2025-08-01 14:37:50,314 - DEBUG - 批次 18 设备: cuda:0
2025-08-01 14:37:50,314 - DEBUG - 批次 18 数据范围: [0.000, 1.000]
2025-08-01 14:37:50,315 - WARNING - 批次 18 处理失败，重试 2/3
2025-08-01 14:37:50,315 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:50,315 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:37:50,315 - DEBUG - 已清理GPU缓存
2025-08-01 14:37:50,817 - DEBUG - 批次 18 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:50,817 - DEBUG - 批次 18 数据类型: torch.float32
2025-08-01 14:37:50,817 - DEBUG - 批次 18 设备: cuda:0
2025-08-01 14:37:50,818 - DEBUG - 批次 18 数据范围: [0.000, 1.000]
2025-08-01 14:37:50,819 - ERROR - 批次 18 处理失败，已重试 3 次
2025-08-01 14:37:50,819 - ERROR - 错误类型: ValueError
2025-08-01 14:37:50,819 - ERROR - 错误信息: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:50,819 - ERROR - 详细堆栈跟踪:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:37:50,819 - ERROR - 失败批次信息:
2025-08-01 14:37:50,820 - ERROR -   - 批次索引: 18
2025-08-01 14:37:50,820 - ERROR -   - 批次大小: 2
2025-08-01 14:37:50,820 - ERROR -   - 无法获取批次详细信息
2025-08-01 14:37:50,821 - DEBUG - 批次 19 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:50,821 - DEBUG - 批次 19 数据类型: torch.float32
2025-08-01 14:37:50,821 - DEBUG - 批次 19 设备: cuda:0
2025-08-01 14:37:50,821 - DEBUG - 批次 19 数据范围: [0.000, 1.000]
2025-08-01 14:37:50,821 - WARNING - 批次 19 处理失败，重试 1/3
2025-08-01 14:37:50,821 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:50,822 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:37:50,822 - DEBUG - 已清理GPU缓存
2025-08-01 14:37:50,853 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:37:50,880 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:37:51,323 - DEBUG - 批次 19 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:51,323 - DEBUG - 批次 19 数据类型: torch.float32
2025-08-01 14:37:51,324 - DEBUG - 批次 19 设备: cuda:0
2025-08-01 14:37:51,324 - DEBUG - 批次 19 数据范围: [0.000, 1.000]
2025-08-01 14:37:51,325 - WARNING - 批次 19 处理失败，重试 2/3
2025-08-01 14:37:51,325 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:51,325 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:37:51,325 - DEBUG - 已清理GPU缓存
2025-08-01 14:37:51,826 - DEBUG - 批次 19 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:51,826 - DEBUG - 批次 19 数据类型: torch.float32
2025-08-01 14:37:51,827 - DEBUG - 批次 19 设备: cuda:0
2025-08-01 14:37:51,827 - DEBUG - 批次 19 数据范围: [0.000, 1.000]
2025-08-01 14:37:51,827 - ERROR - 批次 19 处理失败，已重试 3 次
2025-08-01 14:37:51,827 - ERROR - 错误类型: ValueError
2025-08-01 14:37:51,827 - ERROR - 错误信息: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:51,827 - ERROR - 详细堆栈跟踪:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:37:51,828 - ERROR - 失败批次信息:
2025-08-01 14:37:51,828 - ERROR -   - 批次索引: 19
2025-08-01 14:37:51,828 - ERROR -   - 批次大小: 2
2025-08-01 14:37:51,828 - ERROR -   - 无法获取批次详细信息
2025-08-01 14:37:51,829 - DEBUG - 批次 20 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:51,829 - DEBUG - 批次 20 数据类型: torch.float32
2025-08-01 14:37:51,829 - DEBUG - 批次 20 设备: cuda:0
2025-08-01 14:37:51,829 - DEBUG - 批次 20 数据范围: [0.000, 1.000]
2025-08-01 14:37:51,829 - WARNING - 批次 20 处理失败，重试 1/3
2025-08-01 14:37:51,829 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:51,829 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:37:51,829 - DEBUG - 已清理GPU缓存
2025-08-01 14:37:51,863 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:37:51,889 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:37:52,331 - DEBUG - 批次 20 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:52,331 - DEBUG - 批次 20 数据类型: torch.float32
2025-08-01 14:37:52,331 - DEBUG - 批次 20 设备: cuda:0
2025-08-01 14:37:52,332 - DEBUG - 批次 20 数据范围: [0.000, 1.000]
2025-08-01 14:37:52,332 - WARNING - 批次 20 处理失败，重试 2/3
2025-08-01 14:37:52,333 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:52,333 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:37:52,333 - DEBUG - 已清理GPU缓存
2025-08-01 14:37:52,834 - DEBUG - 批次 20 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:52,835 - DEBUG - 批次 20 数据类型: torch.float32
2025-08-01 14:37:52,835 - DEBUG - 批次 20 设备: cuda:0
2025-08-01 14:37:52,835 - DEBUG - 批次 20 数据范围: [0.000, 1.000]
2025-08-01 14:37:52,836 - ERROR - 批次 20 处理失败，已重试 3 次
2025-08-01 14:37:52,836 - ERROR - 错误类型: ValueError
2025-08-01 14:37:52,837 - ERROR - 错误信息: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:52,837 - ERROR - 详细堆栈跟踪:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:37:52,837 - ERROR - 失败批次信息:
2025-08-01 14:37:52,837 - ERROR -   - 批次索引: 20
2025-08-01 14:37:52,837 - ERROR -   - 批次大小: 2
2025-08-01 14:37:52,837 - ERROR -   - 无法获取批次详细信息
2025-08-01 14:37:52,839 - DEBUG - 批次 21 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:52,839 - DEBUG - 批次 21 数据类型: torch.float32
2025-08-01 14:37:52,839 - DEBUG - 批次 21 设备: cuda:0
2025-08-01 14:37:52,839 - DEBUG - 批次 21 数据范围: [0.000, 0.992]
2025-08-01 14:37:52,839 - WARNING - 批次 21 处理失败，重试 1/3
2025-08-01 14:37:52,839 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:52,839 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:37:52,839 - DEBUG - 已清理GPU缓存
2025-08-01 14:37:52,872 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:37:52,899 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:37:53,341 - DEBUG - 批次 21 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:53,341 - DEBUG - 批次 21 数据类型: torch.float32
2025-08-01 14:37:53,341 - DEBUG - 批次 21 设备: cuda:0
2025-08-01 14:37:53,342 - DEBUG - 批次 21 数据范围: [0.000, 0.992]
2025-08-01 14:37:53,343 - WARNING - 批次 21 处理失败，重试 2/3
2025-08-01 14:37:53,343 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:53,343 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:37:53,343 - DEBUG - 已清理GPU缓存
2025-08-01 14:37:53,845 - DEBUG - 批次 21 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:53,845 - DEBUG - 批次 21 数据类型: torch.float32
2025-08-01 14:37:53,845 - DEBUG - 批次 21 设备: cuda:0
2025-08-01 14:37:53,846 - DEBUG - 批次 21 数据范围: [0.000, 0.992]
2025-08-01 14:37:53,847 - ERROR - 批次 21 处理失败，已重试 3 次
2025-08-01 14:37:53,847 - ERROR - 错误类型: ValueError
2025-08-01 14:37:53,847 - ERROR - 错误信息: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:53,847 - ERROR - 详细堆栈跟踪:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:37:53,847 - ERROR - 失败批次信息:
2025-08-01 14:37:53,847 - ERROR -   - 批次索引: 21
2025-08-01 14:37:53,848 - ERROR -   - 批次大小: 2
2025-08-01 14:37:53,848 - ERROR -   - 无法获取批次详细信息
2025-08-01 14:37:53,848 - DEBUG - 批次 22 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:53,849 - DEBUG - 批次 22 数据类型: torch.float32
2025-08-01 14:37:53,849 - DEBUG - 批次 22 设备: cuda:0
2025-08-01 14:37:53,849 - DEBUG - 批次 22 数据范围: [0.000, 1.000]
2025-08-01 14:37:53,849 - WARNING - 批次 22 处理失败，重试 1/3
2025-08-01 14:37:53,849 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:53,849 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:37:53,849 - DEBUG - 已清理GPU缓存
2025-08-01 14:37:53,879 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:37:53,907 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:37:54,351 - DEBUG - 批次 22 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:54,351 - DEBUG - 批次 22 数据类型: torch.float32
2025-08-01 14:37:54,351 - DEBUG - 批次 22 设备: cuda:0
2025-08-01 14:37:54,352 - DEBUG - 批次 22 数据范围: [0.000, 1.000]
2025-08-01 14:37:54,352 - WARNING - 批次 22 处理失败，重试 2/3
2025-08-01 14:37:54,352 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:54,352 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:37:54,352 - DEBUG - 已清理GPU缓存
2025-08-01 14:37:54,854 - DEBUG - 批次 22 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:54,854 - DEBUG - 批次 22 数据类型: torch.float32
2025-08-01 14:37:54,854 - DEBUG - 批次 22 设备: cuda:0
2025-08-01 14:37:54,855 - DEBUG - 批次 22 数据范围: [0.000, 1.000]
2025-08-01 14:37:54,856 - ERROR - 批次 22 处理失败，已重试 3 次
2025-08-01 14:37:54,856 - ERROR - 错误类型: ValueError
2025-08-01 14:37:54,856 - ERROR - 错误信息: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:54,856 - ERROR - 详细堆栈跟踪:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:37:54,856 - ERROR - 失败批次信息:
2025-08-01 14:37:54,856 - ERROR -   - 批次索引: 22
2025-08-01 14:37:54,857 - ERROR -   - 批次大小: 2
2025-08-01 14:37:54,857 - ERROR -   - 无法获取批次详细信息
2025-08-01 14:37:54,858 - DEBUG - 批次 23 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:54,858 - DEBUG - 批次 23 数据类型: torch.float32
2025-08-01 14:37:54,858 - DEBUG - 批次 23 设备: cuda:0
2025-08-01 14:37:54,858 - DEBUG - 批次 23 数据范围: [0.000, 1.000]
2025-08-01 14:37:54,858 - WARNING - 批次 23 处理失败，重试 1/3
2025-08-01 14:37:54,858 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:54,858 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:37:54,859 - DEBUG - 已清理GPU缓存
2025-08-01 14:37:54,891 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:37:54,916 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:37:55,360 - DEBUG - 批次 23 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:55,360 - DEBUG - 批次 23 数据类型: torch.float32
2025-08-01 14:37:55,360 - DEBUG - 批次 23 设备: cuda:0
2025-08-01 14:37:55,361 - DEBUG - 批次 23 数据范围: [0.000, 1.000]
2025-08-01 14:37:55,361 - WARNING - 批次 23 处理失败，重试 2/3
2025-08-01 14:37:55,362 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:55,362 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:37:55,362 - DEBUG - 已清理GPU缓存
2025-08-01 14:37:55,863 - DEBUG - 批次 23 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:55,863 - DEBUG - 批次 23 数据类型: torch.float32
2025-08-01 14:37:55,864 - DEBUG - 批次 23 设备: cuda:0
2025-08-01 14:37:55,864 - DEBUG - 批次 23 数据范围: [0.000, 1.000]
2025-08-01 14:37:55,865 - ERROR - 批次 23 处理失败，已重试 3 次
2025-08-01 14:37:55,865 - ERROR - 错误类型: ValueError
2025-08-01 14:37:55,865 - ERROR - 错误信息: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:55,866 - ERROR - 详细堆栈跟踪:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:37:55,866 - ERROR - 失败批次信息:
2025-08-01 14:37:55,866 - ERROR -   - 批次索引: 23
2025-08-01 14:37:55,866 - ERROR -   - 批次大小: 2
2025-08-01 14:37:55,866 - ERROR -   - 无法获取批次详细信息
2025-08-01 14:37:55,867 - DEBUG - 批次 24 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:55,867 - DEBUG - 批次 24 数据类型: torch.float32
2025-08-01 14:37:55,867 - DEBUG - 批次 24 设备: cuda:0
2025-08-01 14:37:55,867 - DEBUG - 批次 24 数据范围: [0.000, 0.996]
2025-08-01 14:37:55,867 - WARNING - 批次 24 处理失败，重试 1/3
2025-08-01 14:37:55,868 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:55,868 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:37:55,868 - DEBUG - 已清理GPU缓存
2025-08-01 14:37:55,900 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:37:55,928 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:37:56,369 - DEBUG - 批次 24 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:56,369 - DEBUG - 批次 24 数据类型: torch.float32
2025-08-01 14:37:56,370 - DEBUG - 批次 24 设备: cuda:0
2025-08-01 14:37:56,370 - DEBUG - 批次 24 数据范围: [0.000, 0.996]
2025-08-01 14:37:56,371 - WARNING - 批次 24 处理失败，重试 2/3
2025-08-01 14:37:56,371 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:56,371 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:37:56,371 - DEBUG - 已清理GPU缓存
2025-08-01 14:37:56,873 - DEBUG - 批次 24 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:56,873 - DEBUG - 批次 24 数据类型: torch.float32
2025-08-01 14:37:56,873 - DEBUG - 批次 24 设备: cuda:0
2025-08-01 14:37:56,874 - DEBUG - 批次 24 数据范围: [0.000, 0.996]
2025-08-01 14:37:56,875 - ERROR - 批次 24 处理失败，已重试 3 次
2025-08-01 14:37:56,875 - ERROR - 错误类型: ValueError
2025-08-01 14:37:56,875 - ERROR - 错误信息: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:56,875 - ERROR - 详细堆栈跟踪:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:37:56,875 - ERROR - 失败批次信息:
2025-08-01 14:37:56,875 - ERROR -   - 批次索引: 24
2025-08-01 14:37:56,875 - ERROR -   - 批次大小: 2
2025-08-01 14:37:56,875 - ERROR -   - 无法获取批次详细信息
2025-08-01 14:37:56,876 - DEBUG - 批次 25 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:56,876 - DEBUG - 批次 25 数据类型: torch.float32
2025-08-01 14:37:56,876 - DEBUG - 批次 25 设备: cuda:0
2025-08-01 14:37:56,876 - DEBUG - 批次 25 数据范围: [0.000, 1.000]
2025-08-01 14:37:56,877 - WARNING - 批次 25 处理失败，重试 1/3
2025-08-01 14:37:56,877 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:56,877 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:37:56,877 - DEBUG - 已清理GPU缓存
2025-08-01 14:37:56,910 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:37:56,936 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:37:57,378 - DEBUG - 批次 25 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:57,379 - DEBUG - 批次 25 数据类型: torch.float32
2025-08-01 14:37:57,379 - DEBUG - 批次 25 设备: cuda:0
2025-08-01 14:37:57,380 - DEBUG - 批次 25 数据范围: [0.000, 1.000]
2025-08-01 14:37:57,380 - WARNING - 批次 25 处理失败，重试 2/3
2025-08-01 14:37:57,380 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:57,380 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:37:57,380 - DEBUG - 已清理GPU缓存
2025-08-01 14:37:57,882 - DEBUG - 批次 25 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:57,882 - DEBUG - 批次 25 数据类型: torch.float32
2025-08-01 14:37:57,882 - DEBUG - 批次 25 设备: cuda:0
2025-08-01 14:37:57,883 - DEBUG - 批次 25 数据范围: [0.000, 1.000]
2025-08-01 14:37:57,884 - ERROR - 批次 25 处理失败，已重试 3 次
2025-08-01 14:37:57,884 - ERROR - 错误类型: ValueError
2025-08-01 14:37:57,884 - ERROR - 错误信息: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:57,884 - ERROR - 详细堆栈跟踪:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:37:57,884 - ERROR - 失败批次信息:
2025-08-01 14:37:57,884 - ERROR -   - 批次索引: 25
2025-08-01 14:37:57,884 - ERROR -   - 批次大小: 2
2025-08-01 14:37:57,885 - ERROR -   - 无法获取批次详细信息
2025-08-01 14:37:57,885 - DEBUG - 批次 26 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:57,886 - DEBUG - 批次 26 数据类型: torch.float32
2025-08-01 14:37:57,886 - DEBUG - 批次 26 设备: cuda:0
2025-08-01 14:37:57,886 - DEBUG - 批次 26 数据范围: [0.000, 0.996]
2025-08-01 14:37:57,886 - WARNING - 批次 26 处理失败，重试 1/3
2025-08-01 14:37:57,886 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:57,886 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:37:57,886 - DEBUG - 已清理GPU缓存
2025-08-01 14:37:57,920 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:37:57,948 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:37:58,388 - DEBUG - 批次 26 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:58,388 - DEBUG - 批次 26 数据类型: torch.float32
2025-08-01 14:37:58,388 - DEBUG - 批次 26 设备: cuda:0
2025-08-01 14:37:58,389 - DEBUG - 批次 26 数据范围: [0.000, 0.996]
2025-08-01 14:37:58,389 - WARNING - 批次 26 处理失败，重试 2/3
2025-08-01 14:37:58,390 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:58,390 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:37:58,390 - DEBUG - 已清理GPU缓存
2025-08-01 14:37:58,891 - DEBUG - 批次 26 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:58,891 - DEBUG - 批次 26 数据类型: torch.float32
2025-08-01 14:37:58,892 - DEBUG - 批次 26 设备: cuda:0
2025-08-01 14:37:58,892 - DEBUG - 批次 26 数据范围: [0.000, 0.996]
2025-08-01 14:37:58,893 - ERROR - 批次 26 处理失败，已重试 3 次
2025-08-01 14:37:58,893 - ERROR - 错误类型: ValueError
2025-08-01 14:37:58,893 - ERROR - 错误信息: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:58,893 - ERROR - 详细堆栈跟踪:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:37:58,893 - ERROR - 失败批次信息:
2025-08-01 14:37:58,894 - ERROR -   - 批次索引: 26
2025-08-01 14:37:58,894 - ERROR -   - 批次大小: 2
2025-08-01 14:37:58,894 - ERROR -   - 无法获取批次详细信息
2025-08-01 14:37:58,895 - DEBUG - 批次 27 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:58,895 - DEBUG - 批次 27 数据类型: torch.float32
2025-08-01 14:37:58,895 - DEBUG - 批次 27 设备: cuda:0
2025-08-01 14:37:58,895 - DEBUG - 批次 27 数据范围: [0.000, 0.996]
2025-08-01 14:37:58,896 - WARNING - 批次 27 处理失败，重试 1/3
2025-08-01 14:37:58,896 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:58,896 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:37:58,896 - DEBUG - 已清理GPU缓存
2025-08-01 14:37:58,928 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:37:58,954 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:37:59,397 - DEBUG - 批次 27 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:59,398 - DEBUG - 批次 27 数据类型: torch.float32
2025-08-01 14:37:59,398 - DEBUG - 批次 27 设备: cuda:0
2025-08-01 14:37:59,398 - DEBUG - 批次 27 数据范围: [0.000, 0.996]
2025-08-01 14:37:59,399 - WARNING - 批次 27 处理失败，重试 2/3
2025-08-01 14:37:59,399 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:59,399 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:37:59,399 - DEBUG - 已清理GPU缓存
2025-08-01 14:37:59,901 - DEBUG - 批次 27 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:59,901 - DEBUG - 批次 27 数据类型: torch.float32
2025-08-01 14:37:59,901 - DEBUG - 批次 27 设备: cuda:0
2025-08-01 14:37:59,902 - DEBUG - 批次 27 数据范围: [0.000, 0.996]
2025-08-01 14:37:59,903 - ERROR - 批次 27 处理失败，已重试 3 次
2025-08-01 14:37:59,903 - ERROR - 错误类型: ValueError
2025-08-01 14:37:59,903 - ERROR - 错误信息: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:59,903 - ERROR - 详细堆栈跟踪:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:37:59,903 - ERROR - 失败批次信息:
2025-08-01 14:37:59,903 - ERROR -   - 批次索引: 27
2025-08-01 14:37:59,904 - ERROR -   - 批次大小: 2
2025-08-01 14:37:59,904 - ERROR -   - 无法获取批次详细信息
2025-08-01 14:37:59,905 - DEBUG - 批次 28 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:59,905 - DEBUG - 批次 28 数据类型: torch.float32
2025-08-01 14:37:59,905 - DEBUG - 批次 28 设备: cuda:0
2025-08-01 14:37:59,905 - DEBUG - 批次 28 数据范围: [0.000, 0.996]
2025-08-01 14:37:59,905 - WARNING - 批次 28 处理失败，重试 1/3
2025-08-01 14:37:59,905 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:37:59,905 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:37:59,905 - DEBUG - 已清理GPU缓存
2025-08-01 14:37:59,937 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:37:59,963 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:38:00,407 - DEBUG - 批次 28 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:00,407 - DEBUG - 批次 28 数据类型: torch.float32
2025-08-01 14:38:00,407 - DEBUG - 批次 28 设备: cuda:0
2025-08-01 14:38:00,408 - DEBUG - 批次 28 数据范围: [0.000, 0.996]
2025-08-01 14:38:00,409 - WARNING - 批次 28 处理失败，重试 2/3
2025-08-01 14:38:00,409 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:00,409 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:00,409 - DEBUG - 已清理GPU缓存
2025-08-01 14:38:00,911 - DEBUG - 批次 28 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:00,911 - DEBUG - 批次 28 数据类型: torch.float32
2025-08-01 14:38:00,911 - DEBUG - 批次 28 设备: cuda:0
2025-08-01 14:38:00,912 - DEBUG - 批次 28 数据范围: [0.000, 0.996]
2025-08-01 14:38:00,913 - ERROR - 批次 28 处理失败，已重试 3 次
2025-08-01 14:38:00,913 - ERROR - 错误类型: ValueError
2025-08-01 14:38:00,913 - ERROR - 错误信息: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:00,913 - ERROR - 详细堆栈跟踪:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:00,913 - ERROR - 失败批次信息:
2025-08-01 14:38:00,913 - ERROR -   - 批次索引: 28
2025-08-01 14:38:00,913 - ERROR -   - 批次大小: 2
2025-08-01 14:38:00,913 - ERROR -   - 无法获取批次详细信息
2025-08-01 14:38:00,914 - DEBUG - 批次 29 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:00,914 - DEBUG - 批次 29 数据类型: torch.float32
2025-08-01 14:38:00,914 - DEBUG - 批次 29 设备: cuda:0
2025-08-01 14:38:00,915 - DEBUG - 批次 29 数据范围: [0.000, 0.992]
2025-08-01 14:38:00,915 - WARNING - 批次 29 处理失败，重试 1/3
2025-08-01 14:38:00,915 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:00,915 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:00,915 - DEBUG - 已清理GPU缓存
2025-08-01 14:38:00,944 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:38:00,974 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:38:01,417 - DEBUG - 批次 29 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:01,417 - DEBUG - 批次 29 数据类型: torch.float32
2025-08-01 14:38:01,417 - DEBUG - 批次 29 设备: cuda:0
2025-08-01 14:38:01,418 - DEBUG - 批次 29 数据范围: [0.000, 0.992]
2025-08-01 14:38:01,418 - WARNING - 批次 29 处理失败，重试 2/3
2025-08-01 14:38:01,418 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:01,419 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:01,419 - DEBUG - 已清理GPU缓存
2025-08-01 14:38:01,920 - DEBUG - 批次 29 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:01,920 - DEBUG - 批次 29 数据类型: torch.float32
2025-08-01 14:38:01,921 - DEBUG - 批次 29 设备: cuda:0
2025-08-01 14:38:01,921 - DEBUG - 批次 29 数据范围: [0.000, 0.992]
2025-08-01 14:38:01,922 - ERROR - 批次 29 处理失败，已重试 3 次
2025-08-01 14:38:01,922 - ERROR - 错误类型: ValueError
2025-08-01 14:38:01,922 - ERROR - 错误信息: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:01,922 - ERROR - 详细堆栈跟踪:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:01,922 - ERROR - 失败批次信息:
2025-08-01 14:38:01,922 - ERROR -   - 批次索引: 29
2025-08-01 14:38:01,922 - ERROR -   - 批次大小: 2
2025-08-01 14:38:01,922 - ERROR -   - 无法获取批次详细信息
2025-08-01 14:38:01,924 - DEBUG - 批次 30 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:01,924 - DEBUG - 批次 30 数据类型: torch.float32
2025-08-01 14:38:01,924 - DEBUG - 批次 30 设备: cuda:0
2025-08-01 14:38:01,924 - DEBUG - 批次 30 数据范围: [0.000, 0.992]
2025-08-01 14:38:01,924 - WARNING - 批次 30 处理失败，重试 1/3
2025-08-01 14:38:01,924 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:01,924 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:01,925 - DEBUG - 已清理GPU缓存
2025-08-01 14:38:01,956 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:38:01,983 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:38:02,426 - DEBUG - 批次 30 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:02,426 - DEBUG - 批次 30 数据类型: torch.float32
2025-08-01 14:38:02,426 - DEBUG - 批次 30 设备: cuda:0
2025-08-01 14:38:02,427 - DEBUG - 批次 30 数据范围: [0.000, 0.992]
2025-08-01 14:38:02,428 - WARNING - 批次 30 处理失败，重试 2/3
2025-08-01 14:38:02,428 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:02,428 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:02,428 - DEBUG - 已清理GPU缓存
2025-08-01 14:38:02,930 - DEBUG - 批次 30 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:02,930 - DEBUG - 批次 30 数据类型: torch.float32
2025-08-01 14:38:02,930 - DEBUG - 批次 30 设备: cuda:0
2025-08-01 14:38:02,931 - DEBUG - 批次 30 数据范围: [0.000, 0.992]
2025-08-01 14:38:02,931 - ERROR - 批次 30 处理失败，已重试 3 次
2025-08-01 14:38:02,931 - ERROR - 错误类型: ValueError
2025-08-01 14:38:02,931 - ERROR - 错误信息: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:02,932 - ERROR - 详细堆栈跟踪:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:02,932 - ERROR - 失败批次信息:
2025-08-01 14:38:02,932 - ERROR -   - 批次索引: 30
2025-08-01 14:38:02,932 - ERROR -   - 批次大小: 2
2025-08-01 14:38:02,932 - ERROR -   - 无法获取批次详细信息
2025-08-01 14:38:02,933 - DEBUG - 批次 31 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:02,933 - DEBUG - 批次 31 数据类型: torch.float32
2025-08-01 14:38:02,933 - DEBUG - 批次 31 设备: cuda:0
2025-08-01 14:38:02,933 - DEBUG - 批次 31 数据范围: [0.000, 0.996]
2025-08-01 14:38:02,934 - WARNING - 批次 31 处理失败，重试 1/3
2025-08-01 14:38:02,934 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:02,934 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:02,934 - DEBUG - 已清理GPU缓存
2025-08-01 14:38:02,967 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:38:02,993 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:38:03,436 - DEBUG - 批次 31 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:03,436 - DEBUG - 批次 31 数据类型: torch.float32
2025-08-01 14:38:03,436 - DEBUG - 批次 31 设备: cuda:0
2025-08-01 14:38:03,437 - DEBUG - 批次 31 数据范围: [0.000, 0.996]
2025-08-01 14:38:03,438 - WARNING - 批次 31 处理失败，重试 2/3
2025-08-01 14:38:03,438 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:03,438 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:03,438 - DEBUG - 已清理GPU缓存
2025-08-01 14:38:03,940 - DEBUG - 批次 31 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:03,940 - DEBUG - 批次 31 数据类型: torch.float32
2025-08-01 14:38:03,940 - DEBUG - 批次 31 设备: cuda:0
2025-08-01 14:38:03,941 - DEBUG - 批次 31 数据范围: [0.000, 0.996]
2025-08-01 14:38:03,941 - ERROR - 批次 31 处理失败，已重试 3 次
2025-08-01 14:38:03,941 - ERROR - 错误类型: ValueError
2025-08-01 14:38:03,942 - ERROR - 错误信息: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:03,942 - ERROR - 详细堆栈跟踪:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:03,942 - ERROR - 失败批次信息:
2025-08-01 14:38:03,942 - ERROR -   - 批次索引: 31
2025-08-01 14:38:03,942 - ERROR -   - 批次大小: 2
2025-08-01 14:38:03,942 - ERROR -   - 无法获取批次详细信息
2025-08-01 14:38:03,943 - DEBUG - 批次 32 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:03,943 - DEBUG - 批次 32 数据类型: torch.float32
2025-08-01 14:38:03,943 - DEBUG - 批次 32 设备: cuda:0
2025-08-01 14:38:03,943 - DEBUG - 批次 32 数据范围: [0.000, 0.996]
2025-08-01 14:38:03,944 - WARNING - 批次 32 处理失败，重试 1/3
2025-08-01 14:38:03,944 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:03,944 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:03,944 - DEBUG - 已清理GPU缓存
2025-08-01 14:38:03,975 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:38:04,003 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:38:04,446 - DEBUG - 批次 32 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:04,446 - DEBUG - 批次 32 数据类型: torch.float32
2025-08-01 14:38:04,446 - DEBUG - 批次 32 设备: cuda:0
2025-08-01 14:38:04,447 - DEBUG - 批次 32 数据范围: [0.000, 0.996]
2025-08-01 14:38:04,448 - WARNING - 批次 32 处理失败，重试 2/3
2025-08-01 14:38:04,448 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:04,448 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:04,448 - DEBUG - 已清理GPU缓存
2025-08-01 14:38:04,950 - DEBUG - 批次 32 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:04,950 - DEBUG - 批次 32 数据类型: torch.float32
2025-08-01 14:38:04,950 - DEBUG - 批次 32 设备: cuda:0
2025-08-01 14:38:04,951 - DEBUG - 批次 32 数据范围: [0.000, 0.996]
2025-08-01 14:38:04,951 - ERROR - 批次 32 处理失败，已重试 3 次
2025-08-01 14:38:04,952 - ERROR - 错误类型: ValueError
2025-08-01 14:38:04,952 - ERROR - 错误信息: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:04,952 - ERROR - 详细堆栈跟踪:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:04,952 - ERROR - 失败批次信息:
2025-08-01 14:38:04,952 - ERROR -   - 批次索引: 32
2025-08-01 14:38:04,952 - ERROR -   - 批次大小: 2
2025-08-01 14:38:04,952 - ERROR -   - 无法获取批次详细信息
2025-08-01 14:38:04,954 - DEBUG - 批次 33 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:04,954 - DEBUG - 批次 33 数据类型: torch.float32
2025-08-01 14:38:04,954 - DEBUG - 批次 33 设备: cuda:0
2025-08-01 14:38:04,955 - DEBUG - 批次 33 数据范围: [0.000, 0.996]
2025-08-01 14:38:04,955 - WARNING - 批次 33 处理失败，重试 1/3
2025-08-01 14:38:04,955 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:04,956 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:04,956 - DEBUG - 已清理GPU缓存
2025-08-01 14:38:04,983 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:38:05,009 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:38:05,457 - DEBUG - 批次 33 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:05,457 - DEBUG - 批次 33 数据类型: torch.float32
2025-08-01 14:38:05,458 - DEBUG - 批次 33 设备: cuda:0
2025-08-01 14:38:05,458 - DEBUG - 批次 33 数据范围: [0.000, 0.996]
2025-08-01 14:38:05,458 - WARNING - 批次 33 处理失败，重试 2/3
2025-08-01 14:38:05,459 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:05,459 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:05,459 - DEBUG - 已清理GPU缓存
2025-08-01 14:38:05,960 - DEBUG - 批次 33 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:05,960 - DEBUG - 批次 33 数据类型: torch.float32
2025-08-01 14:38:05,960 - DEBUG - 批次 33 设备: cuda:0
2025-08-01 14:38:05,960 - DEBUG - 批次 33 数据范围: [0.000, 0.996]
2025-08-01 14:38:05,961 - ERROR - 批次 33 处理失败，已重试 3 次
2025-08-01 14:38:05,961 - ERROR - 错误类型: ValueError
2025-08-01 14:38:05,961 - ERROR - 错误信息: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:05,961 - ERROR - 详细堆栈跟踪:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:05,961 - ERROR - 失败批次信息:
2025-08-01 14:38:05,961 - ERROR -   - 批次索引: 33
2025-08-01 14:38:05,961 - ERROR -   - 批次大小: 2
2025-08-01 14:38:05,961 - ERROR -   - 无法获取批次详细信息
2025-08-01 14:38:05,962 - DEBUG - 批次 34 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:05,962 - DEBUG - 批次 34 数据类型: torch.float32
2025-08-01 14:38:05,962 - DEBUG - 批次 34 设备: cuda:0
2025-08-01 14:38:05,962 - DEBUG - 批次 34 数据范围: [0.000, 0.996]
2025-08-01 14:38:05,963 - WARNING - 批次 34 处理失败，重试 1/3
2025-08-01 14:38:05,963 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:05,963 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:05,963 - DEBUG - 已清理GPU缓存
2025-08-01 14:38:05,993 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:38:06,018 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:38:06,464 - DEBUG - 批次 34 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:06,465 - DEBUG - 批次 34 数据类型: torch.float32
2025-08-01 14:38:06,465 - DEBUG - 批次 34 设备: cuda:0
2025-08-01 14:38:06,466 - DEBUG - 批次 34 数据范围: [0.000, 0.996]
2025-08-01 14:38:06,466 - WARNING - 批次 34 处理失败，重试 2/3
2025-08-01 14:38:06,467 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:06,467 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:06,467 - DEBUG - 已清理GPU缓存
2025-08-01 14:38:06,968 - DEBUG - 批次 34 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:06,969 - DEBUG - 批次 34 数据类型: torch.float32
2025-08-01 14:38:06,969 - DEBUG - 批次 34 设备: cuda:0
2025-08-01 14:38:06,969 - DEBUG - 批次 34 数据范围: [0.000, 0.996]
2025-08-01 14:38:06,970 - ERROR - 批次 34 处理失败，已重试 3 次
2025-08-01 14:38:06,970 - ERROR - 错误类型: ValueError
2025-08-01 14:38:06,971 - ERROR - 错误信息: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:06,971 - ERROR - 详细堆栈跟踪:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:06,971 - ERROR - 失败批次信息:
2025-08-01 14:38:06,971 - ERROR -   - 批次索引: 34
2025-08-01 14:38:06,971 - ERROR -   - 批次大小: 2
2025-08-01 14:38:06,971 - ERROR -   - 无法获取批次详细信息
2025-08-01 14:38:06,973 - DEBUG - 批次 35 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:06,973 - DEBUG - 批次 35 数据类型: torch.float32
2025-08-01 14:38:06,973 - DEBUG - 批次 35 设备: cuda:0
2025-08-01 14:38:06,974 - DEBUG - 批次 35 数据范围: [0.000, 0.996]
2025-08-01 14:38:06,975 - WARNING - 批次 35 处理失败，重试 1/3
2025-08-01 14:38:06,975 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:06,975 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:06,975 - DEBUG - 已清理GPU缓存
2025-08-01 14:38:07,001 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:38:07,029 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:38:07,477 - DEBUG - 批次 35 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:07,477 - DEBUG - 批次 35 数据类型: torch.float32
2025-08-01 14:38:07,477 - DEBUG - 批次 35 设备: cuda:0
2025-08-01 14:38:07,478 - DEBUG - 批次 35 数据范围: [0.000, 0.996]
2025-08-01 14:38:07,478 - WARNING - 批次 35 处理失败，重试 2/3
2025-08-01 14:38:07,479 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:07,479 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:07,479 - DEBUG - 已清理GPU缓存
2025-08-01 14:38:07,980 - DEBUG - 批次 35 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:07,981 - DEBUG - 批次 35 数据类型: torch.float32
2025-08-01 14:38:07,981 - DEBUG - 批次 35 设备: cuda:0
2025-08-01 14:38:07,982 - DEBUG - 批次 35 数据范围: [0.000, 0.996]
2025-08-01 14:38:07,982 - ERROR - 批次 35 处理失败，已重试 3 次
2025-08-01 14:38:07,983 - ERROR - 错误类型: ValueError
2025-08-01 14:38:07,983 - ERROR - 错误信息: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:07,983 - ERROR - 详细堆栈跟踪:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:07,983 - ERROR - 失败批次信息:
2025-08-01 14:38:07,983 - ERROR -   - 批次索引: 35
2025-08-01 14:38:07,983 - ERROR -   - 批次大小: 2
2025-08-01 14:38:07,983 - ERROR -   - 无法获取批次详细信息
2025-08-01 14:38:07,985 - DEBUG - 批次 36 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:07,985 - DEBUG - 批次 36 数据类型: torch.float32
2025-08-01 14:38:07,985 - DEBUG - 批次 36 设备: cuda:0
2025-08-01 14:38:07,986 - DEBUG - 批次 36 数据范围: [0.000, 1.000]
2025-08-01 14:38:07,986 - WARNING - 批次 36 处理失败，重试 1/3
2025-08-01 14:38:07,987 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:07,987 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:07,987 - DEBUG - 已清理GPU缓存
2025-08-01 14:38:08,013 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:38:08,039 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:38:08,488 - DEBUG - 批次 36 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:08,489 - DEBUG - 批次 36 数据类型: torch.float32
2025-08-01 14:38:08,489 - DEBUG - 批次 36 设备: cuda:0
2025-08-01 14:38:08,489 - DEBUG - 批次 36 数据范围: [0.000, 1.000]
2025-08-01 14:38:08,490 - WARNING - 批次 36 处理失败，重试 2/3
2025-08-01 14:38:08,491 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:08,491 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:08,491 - DEBUG - 已清理GPU缓存
2025-08-01 14:38:08,992 - DEBUG - 批次 36 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:08,993 - DEBUG - 批次 36 数据类型: torch.float32
2025-08-01 14:38:08,993 - DEBUG - 批次 36 设备: cuda:0
2025-08-01 14:38:08,993 - DEBUG - 批次 36 数据范围: [0.000, 1.000]
2025-08-01 14:38:08,994 - ERROR - 批次 36 处理失败，已重试 3 次
2025-08-01 14:38:08,994 - ERROR - 错误类型: ValueError
2025-08-01 14:38:08,994 - ERROR - 错误信息: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:08,995 - ERROR - 详细堆栈跟踪:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:08,995 - ERROR - 失败批次信息:
2025-08-01 14:38:08,995 - ERROR -   - 批次索引: 36
2025-08-01 14:38:08,995 - ERROR -   - 批次大小: 2
2025-08-01 14:38:08,995 - ERROR -   - 无法获取批次详细信息
2025-08-01 14:38:08,997 - DEBUG - 批次 37 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:08,997 - DEBUG - 批次 37 数据类型: torch.float32
2025-08-01 14:38:08,997 - DEBUG - 批次 37 设备: cuda:0
2025-08-01 14:38:08,997 - DEBUG - 批次 37 数据范围: [0.000, 1.000]
2025-08-01 14:38:08,997 - WARNING - 批次 37 处理失败，重试 1/3
2025-08-01 14:38:08,997 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:08,997 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:08,998 - DEBUG - 已清理GPU缓存
2025-08-01 14:38:09,030 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:38:09,057 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:38:09,499 - DEBUG - 批次 37 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:09,499 - DEBUG - 批次 37 数据类型: torch.float32
2025-08-01 14:38:09,500 - DEBUG - 批次 37 设备: cuda:0
2025-08-01 14:38:09,500 - DEBUG - 批次 37 数据范围: [0.000, 1.000]
2025-08-01 14:38:09,501 - WARNING - 批次 37 处理失败，重试 2/3
2025-08-01 14:38:09,501 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:09,501 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:09,501 - DEBUG - 已清理GPU缓存
2025-08-01 14:38:10,002 - DEBUG - 批次 37 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:10,003 - DEBUG - 批次 37 数据类型: torch.float32
2025-08-01 14:38:10,003 - DEBUG - 批次 37 设备: cuda:0
2025-08-01 14:38:10,003 - DEBUG - 批次 37 数据范围: [0.000, 1.000]
2025-08-01 14:38:10,004 - ERROR - 批次 37 处理失败，已重试 3 次
2025-08-01 14:38:10,004 - ERROR - 错误类型: ValueError
2025-08-01 14:38:10,005 - ERROR - 错误信息: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:10,005 - ERROR - 详细堆栈跟踪:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:10,005 - ERROR - 失败批次信息:
2025-08-01 14:38:10,005 - ERROR -   - 批次索引: 37
2025-08-01 14:38:10,005 - ERROR -   - 批次大小: 2
2025-08-01 14:38:10,005 - ERROR -   - 无法获取批次详细信息
2025-08-01 14:38:10,006 - DEBUG - 批次 38 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:10,006 - DEBUG - 批次 38 数据类型: torch.float32
2025-08-01 14:38:10,006 - DEBUG - 批次 38 设备: cuda:0
2025-08-01 14:38:10,006 - DEBUG - 批次 38 数据范围: [0.000, 1.000]
2025-08-01 14:38:10,007 - WARNING - 批次 38 处理失败，重试 1/3
2025-08-01 14:38:10,007 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:10,007 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:10,007 - DEBUG - 已清理GPU缓存
2025-08-01 14:38:10,037 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:38:10,063 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:38:10,508 - DEBUG - 批次 38 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:10,509 - DEBUG - 批次 38 数据类型: torch.float32
2025-08-01 14:38:10,509 - DEBUG - 批次 38 设备: cuda:0
2025-08-01 14:38:10,509 - DEBUG - 批次 38 数据范围: [0.000, 1.000]
2025-08-01 14:38:10,510 - WARNING - 批次 38 处理失败，重试 2/3
2025-08-01 14:38:10,510 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:10,510 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:10,510 - DEBUG - 已清理GPU缓存
2025-08-01 14:38:11,012 - DEBUG - 批次 38 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:11,012 - DEBUG - 批次 38 数据类型: torch.float32
2025-08-01 14:38:11,012 - DEBUG - 批次 38 设备: cuda:0
2025-08-01 14:38:11,013 - DEBUG - 批次 38 数据范围: [0.000, 1.000]
2025-08-01 14:38:11,013 - ERROR - 批次 38 处理失败，已重试 3 次
2025-08-01 14:38:11,014 - ERROR - 错误类型: ValueError
2025-08-01 14:38:11,014 - ERROR - 错误信息: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:11,014 - ERROR - 详细堆栈跟踪:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:11,014 - ERROR - 失败批次信息:
2025-08-01 14:38:11,014 - ERROR -   - 批次索引: 38
2025-08-01 14:38:11,014 - ERROR -   - 批次大小: 2
2025-08-01 14:38:11,014 - ERROR -   - 无法获取批次详细信息
2025-08-01 14:38:11,015 - DEBUG - 批次 39 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:11,015 - DEBUG - 批次 39 数据类型: torch.float32
2025-08-01 14:38:11,015 - DEBUG - 批次 39 设备: cuda:0
2025-08-01 14:38:11,016 - DEBUG - 批次 39 数据范围: [0.000, 1.000]
2025-08-01 14:38:11,016 - WARNING - 批次 39 处理失败，重试 1/3
2025-08-01 14:38:11,016 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:11,016 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:11,016 - DEBUG - 已清理GPU缓存
2025-08-01 14:38:11,046 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:38:11,071 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:38:11,517 - DEBUG - 批次 39 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:11,518 - DEBUG - 批次 39 数据类型: torch.float32
2025-08-01 14:38:11,518 - DEBUG - 批次 39 设备: cuda:0
2025-08-01 14:38:11,519 - DEBUG - 批次 39 数据范围: [0.000, 1.000]
2025-08-01 14:38:11,519 - WARNING - 批次 39 处理失败，重试 2/3
2025-08-01 14:38:11,520 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:11,520 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:11,520 - DEBUG - 已清理GPU缓存
2025-08-01 14:38:12,021 - DEBUG - 批次 39 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:12,022 - DEBUG - 批次 39 数据类型: torch.float32
2025-08-01 14:38:12,022 - DEBUG - 批次 39 设备: cuda:0
2025-08-01 14:38:12,022 - DEBUG - 批次 39 数据范围: [0.000, 1.000]
2025-08-01 14:38:12,023 - ERROR - 批次 39 处理失败，已重试 3 次
2025-08-01 14:38:12,023 - ERROR - 错误类型: ValueError
2025-08-01 14:38:12,024 - ERROR - 错误信息: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:12,024 - ERROR - 详细堆栈跟踪:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:12,024 - ERROR - 失败批次信息:
2025-08-01 14:38:12,024 - ERROR -   - 批次索引: 39
2025-08-01 14:38:12,024 - ERROR -   - 批次大小: 2
2025-08-01 14:38:12,024 - ERROR -   - 无法获取批次详细信息
2025-08-01 14:38:12,026 - DEBUG - 批次 40 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:12,026 - DEBUG - 批次 40 数据类型: torch.float32
2025-08-01 14:38:12,026 - DEBUG - 批次 40 设备: cuda:0
2025-08-01 14:38:12,026 - DEBUG - 批次 40 数据范围: [0.000, 1.000]
2025-08-01 14:38:12,027 - WARNING - 批次 40 处理失败，重试 1/3
2025-08-01 14:38:12,027 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:12,028 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:12,028 - DEBUG - 已清理GPU缓存
2025-08-01 14:38:12,055 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:38:12,080 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:38:12,529 - DEBUG - 批次 40 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:12,530 - DEBUG - 批次 40 数据类型: torch.float32
2025-08-01 14:38:12,530 - DEBUG - 批次 40 设备: cuda:0
2025-08-01 14:38:12,530 - DEBUG - 批次 40 数据范围: [0.000, 1.000]
2025-08-01 14:38:12,531 - WARNING - 批次 40 处理失败，重试 2/3
2025-08-01 14:38:12,531 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:12,532 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:12,532 - DEBUG - 已清理GPU缓存
2025-08-01 14:38:13,033 - DEBUG - 批次 40 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:13,034 - DEBUG - 批次 40 数据类型: torch.float32
2025-08-01 14:38:13,034 - DEBUG - 批次 40 设备: cuda:0
2025-08-01 14:38:13,034 - DEBUG - 批次 40 数据范围: [0.000, 1.000]
2025-08-01 14:38:13,035 - ERROR - 批次 40 处理失败，已重试 3 次
2025-08-01 14:38:13,035 - ERROR - 错误类型: ValueError
2025-08-01 14:38:13,035 - ERROR - 错误信息: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:13,036 - ERROR - 详细堆栈跟踪:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:13,036 - ERROR - 失败批次信息:
2025-08-01 14:38:13,036 - ERROR -   - 批次索引: 40
2025-08-01 14:38:13,036 - ERROR -   - 批次大小: 2
2025-08-01 14:38:13,036 - ERROR -   - 无法获取批次详细信息
2025-08-01 14:38:13,038 - DEBUG - 批次 41 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:13,038 - DEBUG - 批次 41 数据类型: torch.float32
2025-08-01 14:38:13,038 - DEBUG - 批次 41 设备: cuda:0
2025-08-01 14:38:13,038 - DEBUG - 批次 41 数据范围: [0.000, 1.000]
2025-08-01 14:38:13,039 - WARNING - 批次 41 处理失败，重试 1/3
2025-08-01 14:38:13,039 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:13,040 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:13,040 - DEBUG - 已清理GPU缓存
2025-08-01 14:38:13,066 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:38:13,091 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:38:13,541 - DEBUG - 批次 41 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:13,542 - DEBUG - 批次 41 数据类型: torch.float32
2025-08-01 14:38:13,542 - DEBUG - 批次 41 设备: cuda:0
2025-08-01 14:38:13,542 - DEBUG - 批次 41 数据范围: [0.000, 1.000]
2025-08-01 14:38:13,543 - WARNING - 批次 41 处理失败，重试 2/3
2025-08-01 14:38:13,543 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:13,544 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:13,544 - DEBUG - 已清理GPU缓存
2025-08-01 14:38:14,045 - DEBUG - 批次 41 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:14,046 - DEBUG - 批次 41 数据类型: torch.float32
2025-08-01 14:38:14,046 - DEBUG - 批次 41 设备: cuda:0
2025-08-01 14:38:14,046 - DEBUG - 批次 41 数据范围: [0.000, 1.000]
2025-08-01 14:38:14,047 - ERROR - 批次 41 处理失败，已重试 3 次
2025-08-01 14:38:14,047 - ERROR - 错误类型: ValueError
2025-08-01 14:38:14,047 - ERROR - 错误信息: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:14,048 - ERROR - 详细堆栈跟踪:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:14,048 - ERROR - 失败批次信息:
2025-08-01 14:38:14,048 - ERROR -   - 批次索引: 41
2025-08-01 14:38:14,048 - ERROR -   - 批次大小: 2
2025-08-01 14:38:14,048 - ERROR -   - 无法获取批次详细信息
2025-08-01 14:38:14,050 - DEBUG - 批次 42 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:14,050 - DEBUG - 批次 42 数据类型: torch.float32
2025-08-01 14:38:14,050 - DEBUG - 批次 42 设备: cuda:0
2025-08-01 14:38:14,050 - DEBUG - 批次 42 数据范围: [0.000, 1.000]
2025-08-01 14:38:14,050 - WARNING - 批次 42 处理失败，重试 1/3
2025-08-01 14:38:14,050 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:14,050 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:14,050 - DEBUG - 已清理GPU缓存
2025-08-01 14:38:14,083 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:38:14,109 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:38:14,552 - DEBUG - 批次 42 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:14,552 - DEBUG - 批次 42 数据类型: torch.float32
2025-08-01 14:38:14,552 - DEBUG - 批次 42 设备: cuda:0
2025-08-01 14:38:14,553 - DEBUG - 批次 42 数据范围: [0.000, 1.000]
2025-08-01 14:38:14,553 - WARNING - 批次 42 处理失败，重试 2/3
2025-08-01 14:38:14,554 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:14,554 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:14,554 - DEBUG - 已清理GPU缓存
2025-08-01 14:38:15,055 - DEBUG - 批次 42 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:15,056 - DEBUG - 批次 42 数据类型: torch.float32
2025-08-01 14:38:15,056 - DEBUG - 批次 42 设备: cuda:0
2025-08-01 14:38:15,057 - DEBUG - 批次 42 数据范围: [0.000, 1.000]
2025-08-01 14:38:15,057 - ERROR - 批次 42 处理失败，已重试 3 次
2025-08-01 14:38:15,058 - ERROR - 错误类型: ValueError
2025-08-01 14:38:15,058 - ERROR - 错误信息: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:15,058 - ERROR - 详细堆栈跟踪:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:15,058 - ERROR - 失败批次信息:
2025-08-01 14:38:15,058 - ERROR -   - 批次索引: 42
2025-08-01 14:38:15,058 - ERROR -   - 批次大小: 2
2025-08-01 14:38:15,058 - ERROR -   - 无法获取批次详细信息
2025-08-01 14:38:15,060 - DEBUG - 批次 43 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:15,060 - DEBUG - 批次 43 数据类型: torch.float32
2025-08-01 14:38:15,060 - DEBUG - 批次 43 设备: cuda:0
2025-08-01 14:38:15,060 - DEBUG - 批次 43 数据范围: [0.000, 0.996]
2025-08-01 14:38:15,060 - WARNING - 批次 43 处理失败，重试 1/3
2025-08-01 14:38:15,060 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:15,061 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:15,061 - DEBUG - 已清理GPU缓存
2025-08-01 14:38:15,096 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:38:15,123 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:38:15,562 - DEBUG - 批次 43 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:15,562 - DEBUG - 批次 43 数据类型: torch.float32
2025-08-01 14:38:15,563 - DEBUG - 批次 43 设备: cuda:0
2025-08-01 14:38:15,563 - DEBUG - 批次 43 数据范围: [0.000, 0.996]
2025-08-01 14:38:15,564 - WARNING - 批次 43 处理失败，重试 2/3
2025-08-01 14:38:15,564 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:15,564 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:15,564 - DEBUG - 已清理GPU缓存
2025-08-01 14:38:16,065 - DEBUG - 批次 43 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:16,066 - DEBUG - 批次 43 数据类型: torch.float32
2025-08-01 14:38:16,066 - DEBUG - 批次 43 设备: cuda:0
2025-08-01 14:38:16,066 - DEBUG - 批次 43 数据范围: [0.000, 0.996]
2025-08-01 14:38:16,067 - ERROR - 批次 43 处理失败，已重试 3 次
2025-08-01 14:38:16,067 - ERROR - 错误类型: ValueError
2025-08-01 14:38:16,068 - ERROR - 错误信息: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:16,068 - ERROR - 详细堆栈跟踪:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:16,068 - ERROR - 失败批次信息:
2025-08-01 14:38:16,068 - ERROR -   - 批次索引: 43
2025-08-01 14:38:16,068 - ERROR -   - 批次大小: 2
2025-08-01 14:38:16,068 - ERROR -   - 无法获取批次详细信息
2025-08-01 14:38:16,069 - DEBUG - 批次 44 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:16,069 - DEBUG - 批次 44 数据类型: torch.float32
2025-08-01 14:38:16,069 - DEBUG - 批次 44 设备: cuda:0
2025-08-01 14:38:16,069 - DEBUG - 批次 44 数据范围: [0.000, 0.996]
2025-08-01 14:38:16,069 - WARNING - 批次 44 处理失败，重试 1/3
2025-08-01 14:38:16,069 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:16,069 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:16,070 - DEBUG - 已清理GPU缓存
2025-08-01 14:38:16,100 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:38:16,125 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:38:16,571 - DEBUG - 批次 44 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:16,571 - DEBUG - 批次 44 数据类型: torch.float32
2025-08-01 14:38:16,571 - DEBUG - 批次 44 设备: cuda:0
2025-08-01 14:38:16,572 - DEBUG - 批次 44 数据范围: [0.000, 0.996]
2025-08-01 14:38:16,572 - WARNING - 批次 44 处理失败，重试 2/3
2025-08-01 14:38:16,573 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:16,573 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:16,573 - DEBUG - 已清理GPU缓存
2025-08-01 14:38:17,074 - DEBUG - 批次 44 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:17,074 - DEBUG - 批次 44 数据类型: torch.float32
2025-08-01 14:38:17,075 - DEBUG - 批次 44 设备: cuda:0
2025-08-01 14:38:17,075 - DEBUG - 批次 44 数据范围: [0.000, 0.996]
2025-08-01 14:38:17,076 - ERROR - 批次 44 处理失败，已重试 3 次
2025-08-01 14:38:17,076 - ERROR - 错误类型: ValueError
2025-08-01 14:38:17,076 - ERROR - 错误信息: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:17,077 - ERROR - 详细堆栈跟踪:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:17,077 - ERROR - 失败批次信息:
2025-08-01 14:38:17,077 - ERROR -   - 批次索引: 44
2025-08-01 14:38:17,077 - ERROR -   - 批次大小: 2
2025-08-01 14:38:17,077 - ERROR -   - 无法获取批次详细信息
2025-08-01 14:38:17,078 - DEBUG - 批次 45 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:17,078 - DEBUG - 批次 45 数据类型: torch.float32
2025-08-01 14:38:17,078 - DEBUG - 批次 45 设备: cuda:0
2025-08-01 14:38:17,078 - DEBUG - 批次 45 数据范围: [0.000, 0.996]
2025-08-01 14:38:17,078 - WARNING - 批次 45 处理失败，重试 1/3
2025-08-01 14:38:17,078 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:17,079 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:17,079 - DEBUG - 已清理GPU缓存
2025-08-01 14:38:17,110 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:38:17,136 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:38:17,580 - DEBUG - 批次 45 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:17,580 - DEBUG - 批次 45 数据类型: torch.float32
2025-08-01 14:38:17,581 - DEBUG - 批次 45 设备: cuda:0
2025-08-01 14:38:17,581 - DEBUG - 批次 45 数据范围: [0.000, 0.996]
2025-08-01 14:38:17,582 - WARNING - 批次 45 处理失败，重试 2/3
2025-08-01 14:38:17,582 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:17,582 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:17,582 - DEBUG - 已清理GPU缓存
2025-08-01 14:38:18,084 - DEBUG - 批次 45 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:18,084 - DEBUG - 批次 45 数据类型: torch.float32
2025-08-01 14:38:18,084 - DEBUG - 批次 45 设备: cuda:0
2025-08-01 14:38:18,085 - DEBUG - 批次 45 数据范围: [0.000, 0.996]
2025-08-01 14:38:18,085 - ERROR - 批次 45 处理失败，已重试 3 次
2025-08-01 14:38:18,086 - ERROR - 错误类型: ValueError
2025-08-01 14:38:18,086 - ERROR - 错误信息: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:18,086 - ERROR - 详细堆栈跟踪:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:18,086 - ERROR - 失败批次信息:
2025-08-01 14:38:18,086 - ERROR -   - 批次索引: 45
2025-08-01 14:38:18,086 - ERROR -   - 批次大小: 2
2025-08-01 14:38:18,086 - ERROR -   - 无法获取批次详细信息
2025-08-01 14:38:18,087 - DEBUG - 批次 46 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:18,087 - DEBUG - 批次 46 数据类型: torch.float32
2025-08-01 14:38:18,087 - DEBUG - 批次 46 设备: cuda:0
2025-08-01 14:38:18,087 - DEBUG - 批次 46 数据范围: [0.000, 0.996]
2025-08-01 14:38:18,088 - WARNING - 批次 46 处理失败，重试 1/3
2025-08-01 14:38:18,088 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:18,088 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:18,088 - DEBUG - 已清理GPU缓存
2025-08-01 14:38:18,117 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:38:18,143 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:38:18,589 - DEBUG - 批次 46 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:18,590 - DEBUG - 批次 46 数据类型: torch.float32
2025-08-01 14:38:18,590 - DEBUG - 批次 46 设备: cuda:0
2025-08-01 14:38:18,590 - DEBUG - 批次 46 数据范围: [0.000, 0.996]
2025-08-01 14:38:18,591 - WARNING - 批次 46 处理失败，重试 2/3
2025-08-01 14:38:18,591 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:18,591 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:18,591 - DEBUG - 已清理GPU缓存
2025-08-01 14:38:19,093 - DEBUG - 批次 46 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:19,093 - DEBUG - 批次 46 数据类型: torch.float32
2025-08-01 14:38:19,093 - DEBUG - 批次 46 设备: cuda:0
2025-08-01 14:38:19,094 - DEBUG - 批次 46 数据范围: [0.000, 0.996]
2025-08-01 14:38:19,095 - ERROR - 批次 46 处理失败，已重试 3 次
2025-08-01 14:38:19,095 - ERROR - 错误类型: ValueError
2025-08-01 14:38:19,095 - ERROR - 错误信息: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:19,095 - ERROR - 详细堆栈跟踪:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:19,095 - ERROR - 失败批次信息:
2025-08-01 14:38:19,095 - ERROR -   - 批次索引: 46
2025-08-01 14:38:19,095 - ERROR -   - 批次大小: 2
2025-08-01 14:38:19,095 - ERROR -   - 无法获取批次详细信息
2025-08-01 14:38:19,096 - DEBUG - 批次 47 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:19,096 - DEBUG - 批次 47 数据类型: torch.float32
2025-08-01 14:38:19,096 - DEBUG - 批次 47 设备: cuda:0
2025-08-01 14:38:19,096 - DEBUG - 批次 47 数据范围: [0.000, 0.996]
2025-08-01 14:38:19,097 - WARNING - 批次 47 处理失败，重试 1/3
2025-08-01 14:38:19,097 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:19,097 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:19,097 - DEBUG - 已清理GPU缓存
2025-08-01 14:38:19,128 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:38:19,153 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:38:19,598 - DEBUG - 批次 47 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:19,599 - DEBUG - 批次 47 数据类型: torch.float32
2025-08-01 14:38:19,599 - DEBUG - 批次 47 设备: cuda:0
2025-08-01 14:38:19,599 - DEBUG - 批次 47 数据范围: [0.000, 0.996]
2025-08-01 14:38:19,600 - WARNING - 批次 47 处理失败，重试 2/3
2025-08-01 14:38:19,600 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:19,600 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:19,600 - DEBUG - 已清理GPU缓存
2025-08-01 14:38:20,102 - DEBUG - 批次 47 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:20,102 - DEBUG - 批次 47 数据类型: torch.float32
2025-08-01 14:38:20,102 - DEBUG - 批次 47 设备: cuda:0
2025-08-01 14:38:20,103 - DEBUG - 批次 47 数据范围: [0.000, 0.996]
2025-08-01 14:38:20,103 - ERROR - 批次 47 处理失败，已重试 3 次
2025-08-01 14:38:20,104 - ERROR - 错误类型: ValueError
2025-08-01 14:38:20,104 - ERROR - 错误信息: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:20,104 - ERROR - 详细堆栈跟踪:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:20,104 - ERROR - 失败批次信息:
2025-08-01 14:38:20,104 - ERROR -   - 批次索引: 47
2025-08-01 14:38:20,104 - ERROR -   - 批次大小: 2
2025-08-01 14:38:20,104 - ERROR -   - 无法获取批次详细信息
2025-08-01 14:38:20,105 - DEBUG - 批次 48 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:20,105 - DEBUG - 批次 48 数据类型: torch.float32
2025-08-01 14:38:20,105 - DEBUG - 批次 48 设备: cuda:0
2025-08-01 14:38:20,106 - DEBUG - 批次 48 数据范围: [0.000, 0.996]
2025-08-01 14:38:20,106 - WARNING - 批次 48 处理失败，重试 1/3
2025-08-01 14:38:20,106 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:20,106 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:20,106 - DEBUG - 已清理GPU缓存
2025-08-01 14:38:20,137 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:38:20,164 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:38:20,608 - DEBUG - 批次 48 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:20,608 - DEBUG - 批次 48 数据类型: torch.float32
2025-08-01 14:38:20,608 - DEBUG - 批次 48 设备: cuda:0
2025-08-01 14:38:20,609 - DEBUG - 批次 48 数据范围: [0.000, 0.996]
2025-08-01 14:38:20,610 - WARNING - 批次 48 处理失败，重试 2/3
2025-08-01 14:38:20,610 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:20,610 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:20,610 - DEBUG - 已清理GPU缓存
2025-08-01 14:38:21,112 - DEBUG - 批次 48 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:21,112 - DEBUG - 批次 48 数据类型: torch.float32
2025-08-01 14:38:21,112 - DEBUG - 批次 48 设备: cuda:0
2025-08-01 14:38:21,113 - DEBUG - 批次 48 数据范围: [0.000, 0.996]
2025-08-01 14:38:21,113 - ERROR - 批次 48 处理失败，已重试 3 次
2025-08-01 14:38:21,114 - ERROR - 错误类型: ValueError
2025-08-01 14:38:21,114 - ERROR - 错误信息: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:21,114 - ERROR - 详细堆栈跟踪:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:21,114 - ERROR - 失败批次信息:
2025-08-01 14:38:21,114 - ERROR -   - 批次索引: 48
2025-08-01 14:38:21,114 - ERROR -   - 批次大小: 2
2025-08-01 14:38:21,114 - ERROR -   - 无法获取批次详细信息
2025-08-01 14:38:21,115 - DEBUG - 批次 49 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:21,115 - DEBUG - 批次 49 数据类型: torch.float32
2025-08-01 14:38:21,116 - DEBUG - 批次 49 设备: cuda:0
2025-08-01 14:38:21,116 - DEBUG - 批次 49 数据范围: [0.000, 0.996]
2025-08-01 14:38:21,116 - WARNING - 批次 49 处理失败，重试 1/3
2025-08-01 14:38:21,116 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:21,117 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:21,117 - DEBUG - 已清理GPU缓存
2025-08-01 14:38:21,147 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:38:21,174 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:38:21,618 - DEBUG - 批次 49 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:21,618 - DEBUG - 批次 49 数据类型: torch.float32
2025-08-01 14:38:21,619 - DEBUG - 批次 49 设备: cuda:0
2025-08-01 14:38:21,619 - DEBUG - 批次 49 数据范围: [0.000, 0.996]
2025-08-01 14:38:21,620 - WARNING - 批次 49 处理失败，重试 2/3
2025-08-01 14:38:21,620 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:21,620 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:21,621 - DEBUG - 已清理GPU缓存
2025-08-01 14:38:22,122 - DEBUG - 批次 49 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:22,122 - DEBUG - 批次 49 数据类型: torch.float32
2025-08-01 14:38:22,122 - DEBUG - 批次 49 设备: cuda:0
2025-08-01 14:38:22,123 - DEBUG - 批次 49 数据范围: [0.000, 0.996]
2025-08-01 14:38:22,124 - ERROR - 批次 49 处理失败，已重试 3 次
2025-08-01 14:38:22,124 - ERROR - 错误类型: ValueError
2025-08-01 14:38:22,124 - ERROR - 错误信息: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:22,124 - ERROR - 详细堆栈跟踪:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:22,124 - ERROR - 失败批次信息:
2025-08-01 14:38:22,125 - ERROR -   - 批次索引: 49
2025-08-01 14:38:22,125 - ERROR -   - 批次大小: 2
2025-08-01 14:38:22,125 - ERROR -   - 无法获取批次详细信息
2025-08-01 14:38:22,126 - DEBUG - 批次 50 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:22,127 - DEBUG - 批次 50 数据类型: torch.float32
2025-08-01 14:38:22,127 - DEBUG - 批次 50 设备: cuda:0
2025-08-01 14:38:22,127 - DEBUG - 批次 50 数据范围: [0.000, 0.992]
2025-08-01 14:38:22,128 - WARNING - 批次 50 处理失败，重试 1/3
2025-08-01 14:38:22,128 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:22,128 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:22,128 - DEBUG - 已清理GPU缓存
2025-08-01 14:38:22,156 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:38:22,181 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:38:22,630 - DEBUG - 批次 50 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:22,630 - DEBUG - 批次 50 数据类型: torch.float32
2025-08-01 14:38:22,630 - DEBUG - 批次 50 设备: cuda:0
2025-08-01 14:38:22,631 - DEBUG - 批次 50 数据范围: [0.000, 0.992]
2025-08-01 14:38:22,632 - WARNING - 批次 50 处理失败，重试 2/3
2025-08-01 14:38:22,632 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:22,632 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:22,632 - DEBUG - 已清理GPU缓存
2025-08-01 14:38:23,134 - DEBUG - 批次 50 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:23,134 - DEBUG - 批次 50 数据类型: torch.float32
2025-08-01 14:38:23,134 - DEBUG - 批次 50 设备: cuda:0
2025-08-01 14:38:23,135 - DEBUG - 批次 50 数据范围: [0.000, 0.992]
2025-08-01 14:38:23,136 - ERROR - 批次 50 处理失败，已重试 3 次
2025-08-01 14:38:23,136 - ERROR - 错误类型: ValueError
2025-08-01 14:38:23,136 - ERROR - 错误信息: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:23,136 - ERROR - 详细堆栈跟踪:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:23,136 - ERROR - 失败批次信息:
2025-08-01 14:38:23,136 - ERROR -   - 批次索引: 50
2025-08-01 14:38:23,136 - ERROR -   - 批次大小: 2
2025-08-01 14:38:23,137 - ERROR -   - 无法获取批次详细信息
2025-08-01 14:38:23,138 - DEBUG - 批次 51 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:23,138 - DEBUG - 批次 51 数据类型: torch.float32
2025-08-01 14:38:23,138 - DEBUG - 批次 51 设备: cuda:0
2025-08-01 14:38:23,139 - DEBUG - 批次 51 数据范围: [0.000, 0.992]
2025-08-01 14:38:23,140 - WARNING - 批次 51 处理失败，重试 1/3
2025-08-01 14:38:23,140 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:23,140 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:23,140 - DEBUG - 已清理GPU缓存
2025-08-01 14:38:23,168 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:38:23,193 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:38:23,642 - DEBUG - 批次 51 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:23,642 - DEBUG - 批次 51 数据类型: torch.float32
2025-08-01 14:38:23,642 - DEBUG - 批次 51 设备: cuda:0
2025-08-01 14:38:23,643 - DEBUG - 批次 51 数据范围: [0.000, 0.992]
2025-08-01 14:38:23,643 - WARNING - 批次 51 处理失败，重试 2/3
2025-08-01 14:38:23,644 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:23,644 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:23,644 - DEBUG - 已清理GPU缓存
2025-08-01 14:38:24,145 - DEBUG - 批次 51 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:24,145 - DEBUG - 批次 51 数据类型: torch.float32
2025-08-01 14:38:24,145 - DEBUG - 批次 51 设备: cuda:0
2025-08-01 14:38:24,146 - DEBUG - 批次 51 数据范围: [0.000, 0.992]
2025-08-01 14:38:24,147 - ERROR - 批次 51 处理失败，已重试 3 次
2025-08-01 14:38:24,147 - ERROR - 错误类型: ValueError
2025-08-01 14:38:24,147 - ERROR - 错误信息: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:24,147 - ERROR - 详细堆栈跟踪:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:24,147 - ERROR - 失败批次信息:
2025-08-01 14:38:24,147 - ERROR -   - 批次索引: 51
2025-08-01 14:38:24,148 - ERROR -   - 批次大小: 2
2025-08-01 14:38:24,148 - ERROR -   - 无法获取批次详细信息
2025-08-01 14:38:24,149 - DEBUG - 批次 52 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:24,149 - DEBUG - 批次 52 数据类型: torch.float32
2025-08-01 14:38:24,149 - DEBUG - 批次 52 设备: cuda:0
2025-08-01 14:38:24,149 - DEBUG - 批次 52 数据范围: [0.000, 0.988]
2025-08-01 14:38:24,150 - WARNING - 批次 52 处理失败，重试 1/3
2025-08-01 14:38:24,150 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:24,150 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:24,150 - DEBUG - 已清理GPU缓存
2025-08-01 14:38:24,181 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:38:24,207 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:38:24,651 - DEBUG - 批次 52 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:24,652 - DEBUG - 批次 52 数据类型: torch.float32
2025-08-01 14:38:24,652 - DEBUG - 批次 52 设备: cuda:0
2025-08-01 14:38:24,652 - DEBUG - 批次 52 数据范围: [0.000, 0.988]
2025-08-01 14:38:24,653 - WARNING - 批次 52 处理失败，重试 2/3
2025-08-01 14:38:24,653 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:24,653 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:24,653 - DEBUG - 已清理GPU缓存
2025-08-01 14:38:25,155 - DEBUG - 批次 52 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:25,155 - DEBUG - 批次 52 数据类型: torch.float32
2025-08-01 14:38:25,155 - DEBUG - 批次 52 设备: cuda:0
2025-08-01 14:38:25,156 - DEBUG - 批次 52 数据范围: [0.000, 0.988]
2025-08-01 14:38:25,156 - ERROR - 批次 52 处理失败，已重试 3 次
2025-08-01 14:38:25,157 - ERROR - 错误类型: ValueError
2025-08-01 14:38:25,157 - ERROR - 错误信息: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:25,157 - ERROR - 详细堆栈跟踪:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:25,157 - ERROR - 失败批次信息:
2025-08-01 14:38:25,157 - ERROR -   - 批次索引: 52
2025-08-01 14:38:25,157 - ERROR -   - 批次大小: 2
2025-08-01 14:38:25,157 - ERROR -   - 无法获取批次详细信息
2025-08-01 14:38:25,159 - DEBUG - 批次 53 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:25,159 - DEBUG - 批次 53 数据类型: torch.float32
2025-08-01 14:38:25,159 - DEBUG - 批次 53 设备: cuda:0
2025-08-01 14:38:25,159 - DEBUG - 批次 53 数据范围: [0.000, 0.996]
2025-08-01 14:38:25,159 - WARNING - 批次 53 处理失败，重试 1/3
2025-08-01 14:38:25,159 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:25,159 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:25,159 - DEBUG - 已清理GPU缓存
2025-08-01 14:38:25,191 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:38:25,218 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:38:25,660 - DEBUG - 批次 53 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:25,661 - DEBUG - 批次 53 数据类型: torch.float32
2025-08-01 14:38:25,661 - DEBUG - 批次 53 设备: cuda:0
2025-08-01 14:38:25,661 - DEBUG - 批次 53 数据范围: [0.000, 0.996]
2025-08-01 14:38:25,662 - WARNING - 批次 53 处理失败，重试 2/3
2025-08-01 14:38:25,662 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:25,662 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:25,662 - DEBUG - 已清理GPU缓存
2025-08-01 14:38:26,163 - DEBUG - 批次 53 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:26,163 - DEBUG - 批次 53 数据类型: torch.float32
2025-08-01 14:38:26,164 - DEBUG - 批次 53 设备: cuda:0
2025-08-01 14:38:26,164 - DEBUG - 批次 53 数据范围: [0.000, 0.996]
2025-08-01 14:38:26,165 - ERROR - 批次 53 处理失败，已重试 3 次
2025-08-01 14:38:26,165 - ERROR - 错误类型: ValueError
2025-08-01 14:38:26,165 - ERROR - 错误信息: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:26,165 - ERROR - 详细堆栈跟踪:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:26,166 - ERROR - 失败批次信息:
2025-08-01 14:38:26,166 - ERROR -   - 批次索引: 53
2025-08-01 14:38:26,166 - ERROR -   - 批次大小: 2
2025-08-01 14:38:26,166 - ERROR -   - 无法获取批次详细信息
2025-08-01 14:38:26,167 - DEBUG - 批次 54 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:26,167 - DEBUG - 批次 54 数据类型: torch.float32
2025-08-01 14:38:26,167 - DEBUG - 批次 54 设备: cuda:0
2025-08-01 14:38:26,167 - DEBUG - 批次 54 数据范围: [0.000, 0.996]
2025-08-01 14:38:26,167 - WARNING - 批次 54 处理失败，重试 1/3
2025-08-01 14:38:26,167 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:26,167 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:26,168 - DEBUG - 已清理GPU缓存
2025-08-01 14:38:26,200 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:38:26,225 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:38:26,669 - DEBUG - 批次 54 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:26,669 - DEBUG - 批次 54 数据类型: torch.float32
2025-08-01 14:38:26,669 - DEBUG - 批次 54 设备: cuda:0
2025-08-01 14:38:26,670 - DEBUG - 批次 54 数据范围: [0.000, 0.996]
2025-08-01 14:38:26,671 - WARNING - 批次 54 处理失败，重试 2/3
2025-08-01 14:38:26,671 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:26,671 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:26,671 - DEBUG - 已清理GPU缓存
2025-08-01 14:38:27,172 - DEBUG - 批次 54 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:27,173 - DEBUG - 批次 54 数据类型: torch.float32
2025-08-01 14:38:27,173 - DEBUG - 批次 54 设备: cuda:0
2025-08-01 14:38:27,173 - DEBUG - 批次 54 数据范围: [0.000, 0.996]
2025-08-01 14:38:27,174 - ERROR - 批次 54 处理失败，已重试 3 次
2025-08-01 14:38:27,174 - ERROR - 错误类型: ValueError
2025-08-01 14:38:27,174 - ERROR - 错误信息: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:27,174 - ERROR - 详细堆栈跟踪:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:27,175 - ERROR - 失败批次信息:
2025-08-01 14:38:27,175 - ERROR -   - 批次索引: 54
2025-08-01 14:38:27,175 - ERROR -   - 批次大小: 2
2025-08-01 14:38:27,175 - ERROR -   - 无法获取批次详细信息
2025-08-01 14:38:27,176 - DEBUG - 批次 55 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:27,177 - DEBUG - 批次 55 数据类型: torch.float32
2025-08-01 14:38:27,177 - DEBUG - 批次 55 设备: cuda:0
2025-08-01 14:38:27,177 - DEBUG - 批次 55 数据范围: [0.000, 0.984]
2025-08-01 14:38:27,178 - WARNING - 批次 55 处理失败，重试 1/3
2025-08-01 14:38:27,178 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:27,178 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:27,178 - DEBUG - 已清理GPU缓存
2025-08-01 14:38:27,207 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:38:27,233 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:38:27,680 - DEBUG - 批次 55 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:27,680 - DEBUG - 批次 55 数据类型: torch.float32
2025-08-01 14:38:27,680 - DEBUG - 批次 55 设备: cuda:0
2025-08-01 14:38:27,681 - DEBUG - 批次 55 数据范围: [0.000, 0.984]
2025-08-01 14:38:27,682 - WARNING - 批次 55 处理失败，重试 2/3
2025-08-01 14:38:27,682 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:27,682 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:27,682 - DEBUG - 已清理GPU缓存
2025-08-01 14:38:28,184 - DEBUG - 批次 55 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:28,184 - DEBUG - 批次 55 数据类型: torch.float32
2025-08-01 14:38:28,184 - DEBUG - 批次 55 设备: cuda:0
2025-08-01 14:38:28,185 - DEBUG - 批次 55 数据范围: [0.000, 0.984]
2025-08-01 14:38:28,186 - ERROR - 批次 55 处理失败，已重试 3 次
2025-08-01 14:38:28,186 - ERROR - 错误类型: ValueError
2025-08-01 14:38:28,186 - ERROR - 错误信息: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:28,186 - ERROR - 详细堆栈跟踪:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:28,186 - ERROR - 失败批次信息:
2025-08-01 14:38:28,186 - ERROR -   - 批次索引: 55
2025-08-01 14:38:28,187 - ERROR -   - 批次大小: 2
2025-08-01 14:38:28,187 - ERROR -   - 无法获取批次详细信息
2025-08-01 14:38:28,188 - DEBUG - 批次 56 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:28,188 - DEBUG - 批次 56 数据类型: torch.float32
2025-08-01 14:38:28,188 - DEBUG - 批次 56 设备: cuda:0
2025-08-01 14:38:28,189 - DEBUG - 批次 56 数据范围: [0.000, 1.000]
2025-08-01 14:38:28,190 - WARNING - 批次 56 处理失败，重试 1/3
2025-08-01 14:38:28,190 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:28,190 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:28,190 - DEBUG - 已清理GPU缓存
2025-08-01 14:38:28,215 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:38:28,242 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:38:28,692 - DEBUG - 批次 56 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:28,692 - DEBUG - 批次 56 数据类型: torch.float32
2025-08-01 14:38:28,692 - DEBUG - 批次 56 设备: cuda:0
2025-08-01 14:38:28,693 - DEBUG - 批次 56 数据范围: [0.000, 1.000]
2025-08-01 14:38:28,693 - WARNING - 批次 56 处理失败，重试 2/3
2025-08-01 14:38:28,694 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:28,694 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:28,694 - DEBUG - 已清理GPU缓存
2025-08-01 14:38:29,195 - DEBUG - 批次 56 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:29,196 - DEBUG - 批次 56 数据类型: torch.float32
2025-08-01 14:38:29,196 - DEBUG - 批次 56 设备: cuda:0
2025-08-01 14:38:29,196 - DEBUG - 批次 56 数据范围: [0.000, 1.000]
2025-08-01 14:38:29,197 - ERROR - 批次 56 处理失败，已重试 3 次
2025-08-01 14:38:29,197 - ERROR - 错误类型: ValueError
2025-08-01 14:38:29,197 - ERROR - 错误信息: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:29,198 - ERROR - 详细堆栈跟踪:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:29,198 - ERROR - 失败批次信息:
2025-08-01 14:38:29,198 - ERROR -   - 批次索引: 56
2025-08-01 14:38:29,198 - ERROR -   - 批次大小: 2
2025-08-01 14:38:29,198 - ERROR -   - 无法获取批次详细信息
2025-08-01 14:38:29,200 - DEBUG - 批次 57 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:29,200 - DEBUG - 批次 57 数据类型: torch.float32
2025-08-01 14:38:29,200 - DEBUG - 批次 57 设备: cuda:0
2025-08-01 14:38:29,200 - DEBUG - 批次 57 数据范围: [0.000, 0.996]
2025-08-01 14:38:29,200 - WARNING - 批次 57 处理失败，重试 1/3
2025-08-01 14:38:29,200 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:29,200 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:29,200 - DEBUG - 已清理GPU缓存
2025-08-01 14:38:29,233 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:38:29,260 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:38:29,702 - DEBUG - 批次 57 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:29,702 - DEBUG - 批次 57 数据类型: torch.float32
2025-08-01 14:38:29,702 - DEBUG - 批次 57 设备: cuda:0
2025-08-01 14:38:29,703 - DEBUG - 批次 57 数据范围: [0.000, 0.996]
2025-08-01 14:38:29,704 - WARNING - 批次 57 处理失败，重试 2/3
2025-08-01 14:38:29,704 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:29,704 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:29,704 - DEBUG - 已清理GPU缓存
2025-08-01 14:38:30,205 - DEBUG - 批次 57 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:30,205 - DEBUG - 批次 57 数据类型: torch.float32
2025-08-01 14:38:30,206 - DEBUG - 批次 57 设备: cuda:0
2025-08-01 14:38:30,206 - DEBUG - 批次 57 数据范围: [0.000, 0.996]
2025-08-01 14:38:30,207 - ERROR - 批次 57 处理失败，已重试 3 次
2025-08-01 14:38:30,207 - ERROR - 错误类型: ValueError
2025-08-01 14:38:30,207 - ERROR - 错误信息: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:30,207 - ERROR - 详细堆栈跟踪:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:30,208 - ERROR - 失败批次信息:
2025-08-01 14:38:30,208 - ERROR -   - 批次索引: 57
2025-08-01 14:38:30,208 - ERROR -   - 批次大小: 2
2025-08-01 14:38:30,208 - ERROR -   - 无法获取批次详细信息
2025-08-01 14:38:30,209 - DEBUG - 批次 58 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:30,209 - DEBUG - 批次 58 数据类型: torch.float32
2025-08-01 14:38:30,209 - DEBUG - 批次 58 设备: cuda:0
2025-08-01 14:38:30,209 - DEBUG - 批次 58 数据范围: [0.000, 1.000]
2025-08-01 14:38:30,209 - WARNING - 批次 58 处理失败，重试 1/3
2025-08-01 14:38:30,209 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:30,209 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:30,209 - DEBUG - 已清理GPU缓存
2025-08-01 14:38:30,241 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:38:30,267 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:38:30,711 - DEBUG - 批次 58 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:30,711 - DEBUG - 批次 58 数据类型: torch.float32
2025-08-01 14:38:30,711 - DEBUG - 批次 58 设备: cuda:0
2025-08-01 14:38:30,712 - DEBUG - 批次 58 数据范围: [0.000, 1.000]
2025-08-01 14:38:30,712 - WARNING - 批次 58 处理失败，重试 2/3
2025-08-01 14:38:30,712 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:30,712 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:30,713 - DEBUG - 已清理GPU缓存
2025-08-01 14:38:31,214 - DEBUG - 批次 58 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:31,214 - DEBUG - 批次 58 数据类型: torch.float32
2025-08-01 14:38:31,214 - DEBUG - 批次 58 设备: cuda:0
2025-08-01 14:38:31,215 - DEBUG - 批次 58 数据范围: [0.000, 1.000]
2025-08-01 14:38:31,216 - ERROR - 批次 58 处理失败，已重试 3 次
2025-08-01 14:38:31,216 - ERROR - 错误类型: ValueError
2025-08-01 14:38:31,216 - ERROR - 错误信息: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:31,216 - ERROR - 详细堆栈跟踪:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:31,216 - ERROR - 失败批次信息:
2025-08-01 14:38:31,216 - ERROR -   - 批次索引: 58
2025-08-01 14:38:31,216 - ERROR -   - 批次大小: 2
2025-08-01 14:38:31,217 - ERROR -   - 无法获取批次详细信息
2025-08-01 14:38:31,217 - DEBUG - 批次 59 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:31,217 - DEBUG - 批次 59 数据类型: torch.float32
2025-08-01 14:38:31,218 - DEBUG - 批次 59 设备: cuda:0
2025-08-01 14:38:31,218 - DEBUG - 批次 59 数据范围: [0.000, 1.000]
2025-08-01 14:38:31,218 - WARNING - 批次 59 处理失败，重试 1/3
2025-08-01 14:38:31,218 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:31,218 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:31,218 - DEBUG - 已清理GPU缓存
2025-08-01 14:38:31,245 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:38:31,273 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:38:31,719 - DEBUG - 批次 59 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:31,720 - DEBUG - 批次 59 数据类型: torch.float32
2025-08-01 14:38:31,720 - DEBUG - 批次 59 设备: cuda:0
2025-08-01 14:38:31,721 - DEBUG - 批次 59 数据范围: [0.000, 1.000]
2025-08-01 14:38:31,721 - WARNING - 批次 59 处理失败，重试 2/3
2025-08-01 14:38:31,721 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:31,721 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:31,721 - DEBUG - 已清理GPU缓存
2025-08-01 14:38:32,223 - DEBUG - 批次 59 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:32,223 - DEBUG - 批次 59 数据类型: torch.float32
2025-08-01 14:38:32,223 - DEBUG - 批次 59 设备: cuda:0
2025-08-01 14:38:32,224 - DEBUG - 批次 59 数据范围: [0.000, 1.000]
2025-08-01 14:38:32,225 - ERROR - 批次 59 处理失败，已重试 3 次
2025-08-01 14:38:32,225 - ERROR - 错误类型: ValueError
2025-08-01 14:38:32,225 - ERROR - 错误信息: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:32,225 - ERROR - 详细堆栈跟踪:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:32,225 - ERROR - 失败批次信息:
2025-08-01 14:38:32,225 - ERROR -   - 批次索引: 59
2025-08-01 14:38:32,225 - ERROR -   - 批次大小: 2
2025-08-01 14:38:32,225 - ERROR -   - 无法获取批次详细信息
2025-08-01 14:38:32,226 - DEBUG - 批次 60 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:32,226 - DEBUG - 批次 60 数据类型: torch.float32
2025-08-01 14:38:32,226 - DEBUG - 批次 60 设备: cuda:0
2025-08-01 14:38:32,226 - DEBUG - 批次 60 数据范围: [0.000, 0.996]
2025-08-01 14:38:32,227 - WARNING - 批次 60 处理失败，重试 1/3
2025-08-01 14:38:32,227 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:32,227 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:32,227 - DEBUG - 已清理GPU缓存
2025-08-01 14:38:32,258 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:38:32,284 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:38:32,728 - DEBUG - 批次 60 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:32,728 - DEBUG - 批次 60 数据类型: torch.float32
2025-08-01 14:38:32,729 - DEBUG - 批次 60 设备: cuda:0
2025-08-01 14:38:32,729 - DEBUG - 批次 60 数据范围: [0.000, 0.996]
2025-08-01 14:38:32,730 - WARNING - 批次 60 处理失败，重试 2/3
2025-08-01 14:38:32,730 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:32,730 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:32,730 - DEBUG - 已清理GPU缓存
2025-08-01 14:38:33,231 - DEBUG - 批次 60 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:33,232 - DEBUG - 批次 60 数据类型: torch.float32
2025-08-01 14:38:33,232 - DEBUG - 批次 60 设备: cuda:0
2025-08-01 14:38:33,232 - DEBUG - 批次 60 数据范围: [0.000, 0.996]
2025-08-01 14:38:33,233 - ERROR - 批次 60 处理失败，已重试 3 次
2025-08-01 14:38:33,233 - ERROR - 错误类型: ValueError
2025-08-01 14:38:33,233 - ERROR - 错误信息: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:33,234 - ERROR - 详细堆栈跟踪:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:33,234 - ERROR - 失败批次信息:
2025-08-01 14:38:33,234 - ERROR -   - 批次索引: 60
2025-08-01 14:38:33,234 - ERROR -   - 批次大小: 2
2025-08-01 14:38:33,234 - ERROR -   - 无法获取批次详细信息
2025-08-01 14:38:33,235 - DEBUG - 批次 61 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:33,235 - DEBUG - 批次 61 数据类型: torch.float32
2025-08-01 14:38:33,235 - DEBUG - 批次 61 设备: cuda:0
2025-08-01 14:38:33,235 - DEBUG - 批次 61 数据范围: [0.000, 0.996]
2025-08-01 14:38:33,235 - WARNING - 批次 61 处理失败，重试 1/3
2025-08-01 14:38:33,235 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:33,235 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:33,235 - DEBUG - 已清理GPU缓存
2025-08-01 14:38:33,267 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:38:33,294 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:38:33,736 - DEBUG - 批次 61 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:33,737 - DEBUG - 批次 61 数据类型: torch.float32
2025-08-01 14:38:33,737 - DEBUG - 批次 61 设备: cuda:0
2025-08-01 14:38:33,738 - DEBUG - 批次 61 数据范围: [0.000, 0.996]
2025-08-01 14:38:33,738 - WARNING - 批次 61 处理失败，重试 2/3
2025-08-01 14:38:33,738 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:33,738 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:33,738 - DEBUG - 已清理GPU缓存
2025-08-01 14:38:34,240 - DEBUG - 批次 61 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:34,240 - DEBUG - 批次 61 数据类型: torch.float32
2025-08-01 14:38:34,240 - DEBUG - 批次 61 设备: cuda:0
2025-08-01 14:38:34,241 - DEBUG - 批次 61 数据范围: [0.000, 0.996]
2025-08-01 14:38:34,242 - ERROR - 批次 61 处理失败，已重试 3 次
2025-08-01 14:38:34,242 - ERROR - 错误类型: ValueError
2025-08-01 14:38:34,242 - ERROR - 错误信息: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:34,242 - ERROR - 详细堆栈跟踪:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:34,242 - ERROR - 失败批次信息:
2025-08-01 14:38:34,242 - ERROR -   - 批次索引: 61
2025-08-01 14:38:34,243 - ERROR -   - 批次大小: 2
2025-08-01 14:38:34,243 - ERROR -   - 无法获取批次详细信息
2025-08-01 14:38:34,244 - DEBUG - 批次 62 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:34,244 - DEBUG - 批次 62 数据类型: torch.float32
2025-08-01 14:38:34,244 - DEBUG - 批次 62 设备: cuda:0
2025-08-01 14:38:34,244 - DEBUG - 批次 62 数据范围: [0.000, 0.996]
2025-08-01 14:38:34,244 - WARNING - 批次 62 处理失败，重试 1/3
2025-08-01 14:38:34,244 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:34,244 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:34,244 - DEBUG - 已清理GPU缓存
2025-08-01 14:38:34,276 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:38:34,301 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:38:34,746 - DEBUG - 批次 62 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:34,746 - DEBUG - 批次 62 数据类型: torch.float32
2025-08-01 14:38:34,746 - DEBUG - 批次 62 设备: cuda:0
2025-08-01 14:38:34,746 - DEBUG - 批次 62 数据范围: [0.000, 0.996]
2025-08-01 14:38:34,747 - WARNING - 批次 62 处理失败，重试 2/3
2025-08-01 14:38:34,747 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:34,747 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:34,747 - DEBUG - 已清理GPU缓存
2025-08-01 14:38:35,248 - DEBUG - 批次 62 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:35,248 - DEBUG - 批次 62 数据类型: torch.float32
2025-08-01 14:38:35,249 - DEBUG - 批次 62 设备: cuda:0
2025-08-01 14:38:35,249 - DEBUG - 批次 62 数据范围: [0.000, 0.996]
2025-08-01 14:38:35,250 - ERROR - 批次 62 处理失败，已重试 3 次
2025-08-01 14:38:35,250 - ERROR - 错误类型: ValueError
2025-08-01 14:38:35,250 - ERROR - 错误信息: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:35,250 - ERROR - 详细堆栈跟踪:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:35,251 - ERROR - 失败批次信息:
2025-08-01 14:38:35,251 - ERROR -   - 批次索引: 62
2025-08-01 14:38:35,251 - ERROR -   - 批次大小: 2
2025-08-01 14:38:35,251 - ERROR -   - 无法获取批次详细信息
2025-08-01 14:38:35,252 - DEBUG - 批次 63 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:35,252 - DEBUG - 批次 63 数据类型: torch.float32
2025-08-01 14:38:35,252 - DEBUG - 批次 63 设备: cuda:0
2025-08-01 14:38:35,253 - DEBUG - 批次 63 数据范围: [0.000, 1.000]
2025-08-01 14:38:35,253 - WARNING - 批次 63 处理失败，重试 1/3
2025-08-01 14:38:35,253 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:35,253 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:35,253 - DEBUG - 已清理GPU缓存
2025-08-01 14:38:35,286 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:38:35,312 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:38:35,754 - DEBUG - 批次 63 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:35,755 - DEBUG - 批次 63 数据类型: torch.float32
2025-08-01 14:38:35,755 - DEBUG - 批次 63 设备: cuda:0
2025-08-01 14:38:35,756 - DEBUG - 批次 63 数据范围: [0.000, 1.000]
2025-08-01 14:38:35,756 - WARNING - 批次 63 处理失败，重试 2/3
2025-08-01 14:38:35,757 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:35,757 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:35,757 - DEBUG - 已清理GPU缓存
2025-08-01 14:38:36,258 - DEBUG - 批次 63 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:36,258 - DEBUG - 批次 63 数据类型: torch.float32
2025-08-01 14:38:36,259 - DEBUG - 批次 63 设备: cuda:0
2025-08-01 14:38:36,259 - DEBUG - 批次 63 数据范围: [0.000, 1.000]
2025-08-01 14:38:36,260 - ERROR - 批次 63 处理失败，已重试 3 次
2025-08-01 14:38:36,260 - ERROR - 错误类型: ValueError
2025-08-01 14:38:36,260 - ERROR - 错误信息: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:36,260 - ERROR - 详细堆栈跟踪:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:36,260 - ERROR - 失败批次信息:
2025-08-01 14:38:36,261 - ERROR -   - 批次索引: 63
2025-08-01 14:38:36,261 - ERROR -   - 批次大小: 2
2025-08-01 14:38:36,261 - ERROR -   - 无法获取批次详细信息
2025-08-01 14:38:36,262 - DEBUG - 批次 64 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:36,262 - DEBUG - 批次 64 数据类型: torch.float32
2025-08-01 14:38:36,262 - DEBUG - 批次 64 设备: cuda:0
2025-08-01 14:38:36,262 - DEBUG - 批次 64 数据范围: [0.000, 0.996]
2025-08-01 14:38:36,262 - WARNING - 批次 64 处理失败，重试 1/3
2025-08-01 14:38:36,262 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:36,262 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:36,263 - DEBUG - 已清理GPU缓存
2025-08-01 14:38:36,294 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:38:36,319 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:38:36,764 - DEBUG - 批次 64 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:36,764 - DEBUG - 批次 64 数据类型: torch.float32
2025-08-01 14:38:36,764 - DEBUG - 批次 64 设备: cuda:0
2025-08-01 14:38:36,765 - DEBUG - 批次 64 数据范围: [0.000, 0.996]
2025-08-01 14:38:36,766 - WARNING - 批次 64 处理失败，重试 2/3
2025-08-01 14:38:36,766 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:36,766 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:36,766 - DEBUG - 已清理GPU缓存
2025-08-01 14:38:37,267 - DEBUG - 批次 64 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:37,267 - DEBUG - 批次 64 数据类型: torch.float32
2025-08-01 14:38:37,268 - DEBUG - 批次 64 设备: cuda:0
2025-08-01 14:38:37,268 - DEBUG - 批次 64 数据范围: [0.000, 0.996]
2025-08-01 14:38:37,269 - ERROR - 批次 64 处理失败，已重试 3 次
2025-08-01 14:38:37,269 - ERROR - 错误类型: ValueError
2025-08-01 14:38:37,269 - ERROR - 错误信息: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:37,269 - ERROR - 详细堆栈跟踪:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:37,269 - ERROR - 失败批次信息:
2025-08-01 14:38:37,270 - ERROR -   - 批次索引: 64
2025-08-01 14:38:37,270 - ERROR -   - 批次大小: 2
2025-08-01 14:38:37,270 - ERROR -   - 无法获取批次详细信息
2025-08-01 14:38:37,271 - DEBUG - 批次 65 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:37,271 - DEBUG - 批次 65 数据类型: torch.float32
2025-08-01 14:38:37,271 - DEBUG - 批次 65 设备: cuda:0
2025-08-01 14:38:37,271 - DEBUG - 批次 65 数据范围: [0.000, 0.996]
2025-08-01 14:38:37,271 - WARNING - 批次 65 处理失败，重试 1/3
2025-08-01 14:38:37,271 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:37,271 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:37,271 - DEBUG - 已清理GPU缓存
2025-08-01 14:38:37,302 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:38:37,326 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:38:37,773 - DEBUG - 批次 65 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:37,773 - DEBUG - 批次 65 数据类型: torch.float32
2025-08-01 14:38:37,773 - DEBUG - 批次 65 设备: cuda:0
2025-08-01 14:38:37,774 - DEBUG - 批次 65 数据范围: [0.000, 0.996]
2025-08-01 14:38:37,774 - WARNING - 批次 65 处理失败，重试 2/3
2025-08-01 14:38:37,774 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:37,775 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:37,775 - DEBUG - 已清理GPU缓存
2025-08-01 14:38:38,276 - DEBUG - 批次 65 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:38,276 - DEBUG - 批次 65 数据类型: torch.float32
2025-08-01 14:38:38,277 - DEBUG - 批次 65 设备: cuda:0
2025-08-01 14:38:38,277 - DEBUG - 批次 65 数据范围: [0.000, 0.996]
2025-08-01 14:38:38,278 - ERROR - 批次 65 处理失败，已重试 3 次
2025-08-01 14:38:38,278 - ERROR - 错误类型: ValueError
2025-08-01 14:38:38,278 - ERROR - 错误信息: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:38,278 - ERROR - 详细堆栈跟踪:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:38,278 - ERROR - 失败批次信息:
2025-08-01 14:38:38,278 - ERROR -   - 批次索引: 65
2025-08-01 14:38:38,278 - ERROR -   - 批次大小: 2
2025-08-01 14:38:38,278 - ERROR -   - 无法获取批次详细信息
2025-08-01 14:38:38,279 - DEBUG - 批次 66 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:38,279 - DEBUG - 批次 66 数据类型: torch.float32
2025-08-01 14:38:38,279 - DEBUG - 批次 66 设备: cuda:0
2025-08-01 14:38:38,280 - DEBUG - 批次 66 数据范围: [0.000, 0.996]
2025-08-01 14:38:38,280 - WARNING - 批次 66 处理失败，重试 1/3
2025-08-01 14:38:38,280 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:38,280 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:38,280 - DEBUG - 已清理GPU缓存
2025-08-01 14:38:38,311 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:38:38,336 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:38:38,781 - DEBUG - 批次 66 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:38,782 - DEBUG - 批次 66 数据类型: torch.float32
2025-08-01 14:38:38,782 - DEBUG - 批次 66 设备: cuda:0
2025-08-01 14:38:38,782 - DEBUG - 批次 66 数据范围: [0.000, 0.996]
2025-08-01 14:38:38,783 - WARNING - 批次 66 处理失败，重试 2/3
2025-08-01 14:38:38,783 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:38,783 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:38,783 - DEBUG - 已清理GPU缓存
2025-08-01 14:38:39,285 - DEBUG - 批次 66 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:39,285 - DEBUG - 批次 66 数据类型: torch.float32
2025-08-01 14:38:39,285 - DEBUG - 批次 66 设备: cuda:0
2025-08-01 14:38:39,286 - DEBUG - 批次 66 数据范围: [0.000, 0.996]
2025-08-01 14:38:39,286 - ERROR - 批次 66 处理失败，已重试 3 次
2025-08-01 14:38:39,287 - ERROR - 错误类型: ValueError
2025-08-01 14:38:39,287 - ERROR - 错误信息: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:39,287 - ERROR - 详细堆栈跟踪:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:39,287 - ERROR - 失败批次信息:
2025-08-01 14:38:39,287 - ERROR -   - 批次索引: 66
2025-08-01 14:38:39,287 - ERROR -   - 批次大小: 2
2025-08-01 14:38:39,287 - ERROR -   - 无法获取批次详细信息
2025-08-01 14:38:39,288 - DEBUG - 批次 67 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:39,288 - DEBUG - 批次 67 数据类型: torch.float32
2025-08-01 14:38:39,288 - DEBUG - 批次 67 设备: cuda:0
2025-08-01 14:38:39,289 - DEBUG - 批次 67 数据范围: [0.000, 0.992]
2025-08-01 14:38:39,289 - WARNING - 批次 67 处理失败，重试 1/3
2025-08-01 14:38:39,289 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:39,289 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:39,289 - DEBUG - 已清理GPU缓存
2025-08-01 14:38:39,321 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:38:39,346 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:38:39,790 - DEBUG - 批次 67 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:39,791 - DEBUG - 批次 67 数据类型: torch.float32
2025-08-01 14:38:39,791 - DEBUG - 批次 67 设备: cuda:0
2025-08-01 14:38:39,791 - DEBUG - 批次 67 数据范围: [0.000, 0.992]
2025-08-01 14:38:39,792 - WARNING - 批次 67 处理失败，重试 2/3
2025-08-01 14:38:39,792 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:39,792 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:39,792 - DEBUG - 已清理GPU缓存
2025-08-01 14:38:40,294 - DEBUG - 批次 67 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:40,294 - DEBUG - 批次 67 数据类型: torch.float32
2025-08-01 14:38:40,294 - DEBUG - 批次 67 设备: cuda:0
2025-08-01 14:38:40,295 - DEBUG - 批次 67 数据范围: [0.000, 0.992]
2025-08-01 14:38:40,295 - ERROR - 批次 67 处理失败，已重试 3 次
2025-08-01 14:38:40,296 - ERROR - 错误类型: ValueError
2025-08-01 14:38:40,296 - ERROR - 错误信息: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:40,296 - ERROR - 详细堆栈跟踪:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:40,296 - ERROR - 失败批次信息:
2025-08-01 14:38:40,296 - ERROR -   - 批次索引: 67
2025-08-01 14:38:40,296 - ERROR -   - 批次大小: 2
2025-08-01 14:38:40,296 - ERROR -   - 无法获取批次详细信息
2025-08-01 14:38:40,297 - DEBUG - 批次 68 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:40,297 - DEBUG - 批次 68 数据类型: torch.float32
2025-08-01 14:38:40,297 - DEBUG - 批次 68 设备: cuda:0
2025-08-01 14:38:40,297 - DEBUG - 批次 68 数据范围: [0.000, 1.000]
2025-08-01 14:38:40,297 - WARNING - 批次 68 处理失败，重试 1/3
2025-08-01 14:38:40,298 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:40,298 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:40,298 - DEBUG - 已清理GPU缓存
2025-08-01 14:38:40,328 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:38:40,354 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:38:40,799 - DEBUG - 批次 68 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:40,799 - DEBUG - 批次 68 数据类型: torch.float32
2025-08-01 14:38:40,800 - DEBUG - 批次 68 设备: cuda:0
2025-08-01 14:38:40,800 - DEBUG - 批次 68 数据范围: [0.000, 1.000]
2025-08-01 14:38:40,801 - WARNING - 批次 68 处理失败，重试 2/3
2025-08-01 14:38:40,801 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:40,801 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:40,801 - DEBUG - 已清理GPU缓存
2025-08-01 14:38:41,302 - DEBUG - 批次 68 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:41,303 - DEBUG - 批次 68 数据类型: torch.float32
2025-08-01 14:38:41,303 - DEBUG - 批次 68 设备: cuda:0
2025-08-01 14:38:41,303 - DEBUG - 批次 68 数据范围: [0.000, 1.000]
2025-08-01 14:38:41,304 - ERROR - 批次 68 处理失败，已重试 3 次
2025-08-01 14:38:41,304 - ERROR - 错误类型: ValueError
2025-08-01 14:38:41,304 - ERROR - 错误信息: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:41,304 - ERROR - 详细堆栈跟踪:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:41,304 - ERROR - 失败批次信息:
2025-08-01 14:38:41,304 - ERROR -   - 批次索引: 68
2025-08-01 14:38:41,304 - ERROR -   - 批次大小: 2
2025-08-01 14:38:41,304 - ERROR -   - 无法获取批次详细信息
2025-08-01 14:38:41,305 - DEBUG - 批次 69 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:41,305 - DEBUG - 批次 69 数据类型: torch.float32
2025-08-01 14:38:41,305 - DEBUG - 批次 69 设备: cuda:0
2025-08-01 14:38:41,306 - DEBUG - 批次 69 数据范围: [0.000, 0.996]
2025-08-01 14:38:41,306 - WARNING - 批次 69 处理失败，重试 1/3
2025-08-01 14:38:41,306 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:41,306 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:41,306 - DEBUG - 已清理GPU缓存
2025-08-01 14:38:41,335 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:38:41,362 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:38:41,808 - DEBUG - 批次 69 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:41,808 - DEBUG - 批次 69 数据类型: torch.float32
2025-08-01 14:38:41,808 - DEBUG - 批次 69 设备: cuda:0
2025-08-01 14:38:41,809 - DEBUG - 批次 69 数据范围: [0.000, 0.996]
2025-08-01 14:38:41,810 - WARNING - 批次 69 处理失败，重试 2/3
2025-08-01 14:38:41,810 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:41,810 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:41,810 - DEBUG - 已清理GPU缓存
2025-08-01 14:38:42,312 - DEBUG - 批次 69 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:42,312 - DEBUG - 批次 69 数据类型: torch.float32
2025-08-01 14:38:42,312 - DEBUG - 批次 69 设备: cuda:0
2025-08-01 14:38:42,313 - DEBUG - 批次 69 数据范围: [0.000, 0.996]
2025-08-01 14:38:42,313 - ERROR - 批次 69 处理失败，已重试 3 次
2025-08-01 14:38:42,313 - ERROR - 错误类型: ValueError
2025-08-01 14:38:42,313 - ERROR - 错误信息: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:42,314 - ERROR - 详细堆栈跟踪:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:42,314 - ERROR - 失败批次信息:
2025-08-01 14:38:42,314 - ERROR -   - 批次索引: 69
2025-08-01 14:38:42,314 - ERROR -   - 批次大小: 2
2025-08-01 14:38:42,314 - ERROR -   - 无法获取批次详细信息
2025-08-01 14:38:42,315 - DEBUG - 批次 70 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:42,315 - DEBUG - 批次 70 数据类型: torch.float32
2025-08-01 14:38:42,315 - DEBUG - 批次 70 设备: cuda:0
2025-08-01 14:38:42,316 - DEBUG - 批次 70 数据范围: [0.000, 1.000]
2025-08-01 14:38:42,316 - WARNING - 批次 70 处理失败，重试 1/3
2025-08-01 14:38:42,316 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:42,316 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:42,316 - DEBUG - 已清理GPU缓存
2025-08-01 14:38:42,347 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:38:42,373 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:38:42,818 - DEBUG - 批次 70 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:42,818 - DEBUG - 批次 70 数据类型: torch.float32
2025-08-01 14:38:42,818 - DEBUG - 批次 70 设备: cuda:0
2025-08-01 14:38:42,819 - DEBUG - 批次 70 数据范围: [0.000, 1.000]
2025-08-01 14:38:42,820 - WARNING - 批次 70 处理失败，重试 2/3
2025-08-01 14:38:42,820 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:42,820 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:42,820 - DEBUG - 已清理GPU缓存
2025-08-01 14:38:43,322 - DEBUG - 批次 70 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:43,322 - DEBUG - 批次 70 数据类型: torch.float32
2025-08-01 14:38:43,322 - DEBUG - 批次 70 设备: cuda:0
2025-08-01 14:38:43,323 - DEBUG - 批次 70 数据范围: [0.000, 1.000]
2025-08-01 14:38:43,323 - ERROR - 批次 70 处理失败，已重试 3 次
2025-08-01 14:38:43,324 - ERROR - 错误类型: ValueError
2025-08-01 14:38:43,324 - ERROR - 错误信息: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:43,324 - ERROR - 详细堆栈跟踪:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:43,324 - ERROR - 失败批次信息:
2025-08-01 14:38:43,324 - ERROR -   - 批次索引: 70
2025-08-01 14:38:43,324 - ERROR -   - 批次大小: 2
2025-08-01 14:38:43,324 - ERROR -   - 无法获取批次详细信息
2025-08-01 14:38:43,326 - DEBUG - 批次 71 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:43,326 - DEBUG - 批次 71 数据类型: torch.float32
2025-08-01 14:38:43,326 - DEBUG - 批次 71 设备: cuda:0
2025-08-01 14:38:43,327 - DEBUG - 批次 71 数据范围: [0.000, 0.996]
2025-08-01 14:38:43,327 - WARNING - 批次 71 处理失败，重试 1/3
2025-08-01 14:38:43,327 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:43,328 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:43,328 - DEBUG - 已清理GPU缓存
2025-08-01 14:38:43,357 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:38:43,383 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:38:43,829 - DEBUG - 批次 71 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:43,830 - DEBUG - 批次 71 数据类型: torch.float32
2025-08-01 14:38:43,830 - DEBUG - 批次 71 设备: cuda:0
2025-08-01 14:38:43,830 - DEBUG - 批次 71 数据范围: [0.000, 0.996]
2025-08-01 14:38:43,831 - WARNING - 批次 71 处理失败，重试 2/3
2025-08-01 14:38:43,831 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:43,831 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:43,832 - DEBUG - 已清理GPU缓存
2025-08-01 14:38:44,333 - DEBUG - 批次 71 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:44,333 - DEBUG - 批次 71 数据类型: torch.float32
2025-08-01 14:38:44,334 - DEBUG - 批次 71 设备: cuda:0
2025-08-01 14:38:44,334 - DEBUG - 批次 71 数据范围: [0.000, 0.996]
2025-08-01 14:38:44,335 - ERROR - 批次 71 处理失败，已重试 3 次
2025-08-01 14:38:44,335 - ERROR - 错误类型: ValueError
2025-08-01 14:38:44,335 - ERROR - 错误信息: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:44,335 - ERROR - 详细堆栈跟踪:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:44,335 - ERROR - 失败批次信息:
2025-08-01 14:38:44,336 - ERROR -   - 批次索引: 71
2025-08-01 14:38:44,336 - ERROR -   - 批次大小: 2
2025-08-01 14:38:44,336 - ERROR -   - 无法获取批次详细信息
2025-08-01 14:38:44,337 - DEBUG - 批次 72 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:44,337 - DEBUG - 批次 72 数据类型: torch.float32
2025-08-01 14:38:44,338 - DEBUG - 批次 72 设备: cuda:0
2025-08-01 14:38:44,338 - DEBUG - 批次 72 数据范围: [0.000, 1.000]
2025-08-01 14:38:44,339 - WARNING - 批次 72 处理失败，重试 1/3
2025-08-01 14:38:44,339 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:44,339 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:44,339 - DEBUG - 已清理GPU缓存
2025-08-01 14:38:44,366 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:38:44,393 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:38:44,841 - DEBUG - 批次 72 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:44,841 - DEBUG - 批次 72 数据类型: torch.float32
2025-08-01 14:38:44,841 - DEBUG - 批次 72 设备: cuda:0
2025-08-01 14:38:44,842 - DEBUG - 批次 72 数据范围: [0.000, 1.000]
2025-08-01 14:38:44,843 - WARNING - 批次 72 处理失败，重试 2/3
2025-08-01 14:38:44,843 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:44,843 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:44,843 - DEBUG - 已清理GPU缓存
2025-08-01 14:38:45,345 - DEBUG - 批次 72 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:45,345 - DEBUG - 批次 72 数据类型: torch.float32
2025-08-01 14:38:45,345 - DEBUG - 批次 72 设备: cuda:0
2025-08-01 14:38:45,346 - DEBUG - 批次 72 数据范围: [0.000, 1.000]
2025-08-01 14:38:45,346 - ERROR - 批次 72 处理失败，已重试 3 次
2025-08-01 14:38:45,347 - ERROR - 错误类型: ValueError
2025-08-01 14:38:45,347 - ERROR - 错误信息: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:45,347 - ERROR - 详细堆栈跟踪:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:45,347 - ERROR - 失败批次信息:
2025-08-01 14:38:45,347 - ERROR -   - 批次索引: 72
2025-08-01 14:38:45,347 - ERROR -   - 批次大小: 2
2025-08-01 14:38:45,347 - ERROR -   - 无法获取批次详细信息
2025-08-01 14:38:45,349 - DEBUG - 批次 73 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:45,349 - DEBUG - 批次 73 数据类型: torch.float32
2025-08-01 14:38:45,349 - DEBUG - 批次 73 设备: cuda:0
2025-08-01 14:38:45,350 - DEBUG - 批次 73 数据范围: [0.000, 0.996]
2025-08-01 14:38:45,350 - WARNING - 批次 73 处理失败，重试 1/3
2025-08-01 14:38:45,350 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:45,350 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:45,350 - DEBUG - 已清理GPU缓存
2025-08-01 14:38:45,383 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:38:45,409 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:38:45,852 - DEBUG - 批次 73 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:45,852 - DEBUG - 批次 73 数据类型: torch.float32
2025-08-01 14:38:45,852 - DEBUG - 批次 73 设备: cuda:0
2025-08-01 14:38:45,853 - DEBUG - 批次 73 数据范围: [0.000, 0.996]
2025-08-01 14:38:45,853 - WARNING - 批次 73 处理失败，重试 2/3
2025-08-01 14:38:45,854 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:45,854 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:45,854 - DEBUG - 已清理GPU缓存
2025-08-01 14:38:46,355 - DEBUG - 批次 73 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:46,356 - DEBUG - 批次 73 数据类型: torch.float32
2025-08-01 14:38:46,356 - DEBUG - 批次 73 设备: cuda:0
2025-08-01 14:38:46,356 - DEBUG - 批次 73 数据范围: [0.000, 0.996]
2025-08-01 14:38:46,357 - ERROR - 批次 73 处理失败，已重试 3 次
2025-08-01 14:38:46,357 - ERROR - 错误类型: ValueError
2025-08-01 14:38:46,357 - ERROR - 错误信息: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:46,358 - ERROR - 详细堆栈跟踪:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:46,358 - ERROR - 失败批次信息:
2025-08-01 14:38:46,358 - ERROR -   - 批次索引: 73
2025-08-01 14:38:46,358 - ERROR -   - 批次大小: 2
2025-08-01 14:38:46,358 - ERROR -   - 无法获取批次详细信息
2025-08-01 14:38:46,359 - DEBUG - 批次 74 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:46,359 - DEBUG - 批次 74 数据类型: torch.float32
2025-08-01 14:38:46,359 - DEBUG - 批次 74 设备: cuda:0
2025-08-01 14:38:46,360 - DEBUG - 批次 74 数据范围: [0.000, 0.996]
2025-08-01 14:38:46,360 - WARNING - 批次 74 处理失败，重试 1/3
2025-08-01 14:38:46,360 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:46,360 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:46,360 - DEBUG - 已清理GPU缓存
2025-08-01 14:38:46,392 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:38:46,417 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:38:46,861 - DEBUG - 批次 74 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:46,862 - DEBUG - 批次 74 数据类型: torch.float32
2025-08-01 14:38:46,862 - DEBUG - 批次 74 设备: cuda:0
2025-08-01 14:38:46,862 - DEBUG - 批次 74 数据范围: [0.000, 0.996]
2025-08-01 14:38:46,863 - WARNING - 批次 74 处理失败，重试 2/3
2025-08-01 14:38:46,863 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:46,864 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:46,864 - DEBUG - 已清理GPU缓存
2025-08-01 14:38:47,365 - DEBUG - 批次 74 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:47,365 - DEBUG - 批次 74 数据类型: torch.float32
2025-08-01 14:38:47,366 - DEBUG - 批次 74 设备: cuda:0
2025-08-01 14:38:47,366 - DEBUG - 批次 74 数据范围: [0.000, 0.996]
2025-08-01 14:38:47,367 - ERROR - 批次 74 处理失败，已重试 3 次
2025-08-01 14:38:47,367 - ERROR - 错误类型: ValueError
2025-08-01 14:38:47,367 - ERROR - 错误信息: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:47,367 - ERROR - 详细堆栈跟踪:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:47,368 - ERROR - 失败批次信息:
2025-08-01 14:38:47,368 - ERROR -   - 批次索引: 74
2025-08-01 14:38:47,368 - ERROR -   - 批次大小: 2
2025-08-01 14:38:47,368 - ERROR -   - 无法获取批次详细信息
2025-08-01 14:38:47,369 - DEBUG - 批次 75 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:47,369 - DEBUG - 批次 75 数据类型: torch.float32
2025-08-01 14:38:47,369 - DEBUG - 批次 75 设备: cuda:0
2025-08-01 14:38:47,370 - DEBUG - 批次 75 数据范围: [0.000, 0.996]
2025-08-01 14:38:47,370 - WARNING - 批次 75 处理失败，重试 1/3
2025-08-01 14:38:47,370 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:47,370 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:47,370 - DEBUG - 已清理GPU缓存
2025-08-01 14:38:47,402 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:38:47,428 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:38:47,871 - DEBUG - 批次 75 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:47,872 - DEBUG - 批次 75 数据类型: torch.float32
2025-08-01 14:38:47,872 - DEBUG - 批次 75 设备: cuda:0
2025-08-01 14:38:47,872 - DEBUG - 批次 75 数据范围: [0.000, 0.996]
2025-08-01 14:38:47,873 - WARNING - 批次 75 处理失败，重试 2/3
2025-08-01 14:38:47,873 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:47,874 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:47,874 - DEBUG - 已清理GPU缓存
2025-08-01 14:38:48,375 - DEBUG - 批次 75 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:48,375 - DEBUG - 批次 75 数据类型: torch.float32
2025-08-01 14:38:48,376 - DEBUG - 批次 75 设备: cuda:0
2025-08-01 14:38:48,376 - DEBUG - 批次 75 数据范围: [0.000, 0.996]
2025-08-01 14:38:48,377 - ERROR - 批次 75 处理失败，已重试 3 次
2025-08-01 14:38:48,377 - ERROR - 错误类型: ValueError
2025-08-01 14:38:48,377 - ERROR - 错误信息: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:48,377 - ERROR - 详细堆栈跟踪:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:48,378 - ERROR - 失败批次信息:
2025-08-01 14:38:48,378 - ERROR -   - 批次索引: 75
2025-08-01 14:38:48,378 - ERROR -   - 批次大小: 2
2025-08-01 14:38:48,378 - ERROR -   - 无法获取批次详细信息
2025-08-01 14:38:48,379 - DEBUG - 批次 76 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:48,380 - DEBUG - 批次 76 数据类型: torch.float32
2025-08-01 14:38:48,380 - DEBUG - 批次 76 设备: cuda:0
2025-08-01 14:38:48,380 - DEBUG - 批次 76 数据范围: [0.000, 1.000]
2025-08-01 14:38:48,380 - WARNING - 批次 76 处理失败，重试 1/3
2025-08-01 14:38:48,380 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:48,380 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:48,380 - DEBUG - 已清理GPU缓存
2025-08-01 14:38:48,414 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:38:48,441 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:38:48,882 - DEBUG - 批次 76 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:48,882 - DEBUG - 批次 76 数据类型: torch.float32
2025-08-01 14:38:48,882 - DEBUG - 批次 76 设备: cuda:0
2025-08-01 14:38:48,883 - DEBUG - 批次 76 数据范围: [0.000, 1.000]
2025-08-01 14:38:48,884 - WARNING - 批次 76 处理失败，重试 2/3
2025-08-01 14:38:48,884 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:48,884 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:48,884 - DEBUG - 已清理GPU缓存
2025-08-01 14:38:49,385 - DEBUG - 批次 76 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:49,386 - DEBUG - 批次 76 数据类型: torch.float32
2025-08-01 14:38:49,386 - DEBUG - 批次 76 设备: cuda:0
2025-08-01 14:38:49,387 - DEBUG - 批次 76 数据范围: [0.000, 1.000]
2025-08-01 14:38:49,387 - ERROR - 批次 76 处理失败，已重试 3 次
2025-08-01 14:38:49,388 - ERROR - 错误类型: ValueError
2025-08-01 14:38:49,388 - ERROR - 错误信息: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:49,388 - ERROR - 详细堆栈跟踪:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:49,388 - ERROR - 失败批次信息:
2025-08-01 14:38:49,388 - ERROR -   - 批次索引: 76
2025-08-01 14:38:49,388 - ERROR -   - 批次大小: 2
2025-08-01 14:38:49,388 - ERROR -   - 无法获取批次详细信息
2025-08-01 14:38:49,390 - DEBUG - 批次 77 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:49,390 - DEBUG - 批次 77 数据类型: torch.float32
2025-08-01 14:38:49,390 - DEBUG - 批次 77 设备: cuda:0
2025-08-01 14:38:49,390 - DEBUG - 批次 77 数据范围: [0.000, 1.000]
2025-08-01 14:38:49,391 - WARNING - 批次 77 处理失败，重试 1/3
2025-08-01 14:38:49,391 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:49,391 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:49,391 - DEBUG - 已清理GPU缓存
2025-08-01 14:38:49,423 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:38:49,449 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:38:49,892 - DEBUG - 批次 77 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:49,892 - DEBUG - 批次 77 数据类型: torch.float32
2025-08-01 14:38:49,893 - DEBUG - 批次 77 设备: cuda:0
2025-08-01 14:38:49,893 - DEBUG - 批次 77 数据范围: [0.000, 1.000]
2025-08-01 14:38:49,894 - WARNING - 批次 77 处理失败，重试 2/3
2025-08-01 14:38:49,894 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:49,894 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:49,895 - DEBUG - 已清理GPU缓存
2025-08-01 14:38:50,396 - DEBUG - 批次 77 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:50,396 - DEBUG - 批次 77 数据类型: torch.float32
2025-08-01 14:38:50,396 - DEBUG - 批次 77 设备: cuda:0
2025-08-01 14:38:50,397 - DEBUG - 批次 77 数据范围: [0.000, 1.000]
2025-08-01 14:38:50,398 - ERROR - 批次 77 处理失败，已重试 3 次
2025-08-01 14:38:50,398 - ERROR - 错误类型: ValueError
2025-08-01 14:38:50,398 - ERROR - 错误信息: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:50,398 - ERROR - 详细堆栈跟踪:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:50,398 - ERROR - 失败批次信息:
2025-08-01 14:38:50,398 - ERROR -   - 批次索引: 77
2025-08-01 14:38:50,399 - ERROR -   - 批次大小: 2
2025-08-01 14:38:50,399 - ERROR -   - 无法获取批次详细信息
2025-08-01 14:38:50,400 - DEBUG - 批次 78 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:50,400 - DEBUG - 批次 78 数据类型: torch.float32
2025-08-01 14:38:50,400 - DEBUG - 批次 78 设备: cuda:0
2025-08-01 14:38:50,400 - DEBUG - 批次 78 数据范围: [0.000, 0.996]
2025-08-01 14:38:50,401 - WARNING - 批次 78 处理失败，重试 1/3
2025-08-01 14:38:50,401 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:50,401 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:50,401 - DEBUG - 已清理GPU缓存
2025-08-01 14:38:50,432 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:38:50,458 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:38:50,902 - DEBUG - 批次 78 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:50,902 - DEBUG - 批次 78 数据类型: torch.float32
2025-08-01 14:38:50,903 - DEBUG - 批次 78 设备: cuda:0
2025-08-01 14:38:50,903 - DEBUG - 批次 78 数据范围: [0.000, 0.996]
2025-08-01 14:38:50,904 - WARNING - 批次 78 处理失败，重试 2/3
2025-08-01 14:38:50,904 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:50,904 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:50,904 - DEBUG - 已清理GPU缓存
2025-08-01 14:38:51,406 - DEBUG - 批次 78 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:51,406 - DEBUG - 批次 78 数据类型: torch.float32
2025-08-01 14:38:51,406 - DEBUG - 批次 78 设备: cuda:0
2025-08-01 14:38:51,407 - DEBUG - 批次 78 数据范围: [0.000, 0.996]
2025-08-01 14:38:51,407 - ERROR - 批次 78 处理失败，已重试 3 次
2025-08-01 14:38:51,408 - ERROR - 错误类型: ValueError
2025-08-01 14:38:51,408 - ERROR - 错误信息: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:51,408 - ERROR - 详细堆栈跟踪:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:51,408 - ERROR - 失败批次信息:
2025-08-01 14:38:51,408 - ERROR -   - 批次索引: 78
2025-08-01 14:38:51,408 - ERROR -   - 批次大小: 2
2025-08-01 14:38:51,408 - ERROR -   - 无法获取批次详细信息
2025-08-01 14:38:51,410 - DEBUG - 批次 79 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:51,410 - DEBUG - 批次 79 数据类型: torch.float32
2025-08-01 14:38:51,410 - DEBUG - 批次 79 设备: cuda:0
2025-08-01 14:38:51,410 - DEBUG - 批次 79 数据范围: [0.000, 0.996]
2025-08-01 14:38:51,410 - WARNING - 批次 79 处理失败，重试 1/3
2025-08-01 14:38:51,411 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:51,411 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:51,411 - DEBUG - 已清理GPU缓存
2025-08-01 14:38:51,442 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:38:51,468 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:38:51,912 - DEBUG - 批次 79 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:51,912 - DEBUG - 批次 79 数据类型: torch.float32
2025-08-01 14:38:51,912 - DEBUG - 批次 79 设备: cuda:0
2025-08-01 14:38:51,912 - DEBUG - 批次 79 数据范围: [0.000, 0.996]
2025-08-01 14:38:51,913 - WARNING - 批次 79 处理失败，重试 2/3
2025-08-01 14:38:51,913 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:51,913 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:51,913 - DEBUG - 已清理GPU缓存
2025-08-01 14:38:52,414 - DEBUG - 批次 79 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:52,415 - DEBUG - 批次 79 数据类型: torch.float32
2025-08-01 14:38:52,415 - DEBUG - 批次 79 设备: cuda:0
2025-08-01 14:38:52,415 - DEBUG - 批次 79 数据范围: [0.000, 0.996]
2025-08-01 14:38:52,416 - ERROR - 批次 79 处理失败，已重试 3 次
2025-08-01 14:38:52,416 - ERROR - 错误类型: ValueError
2025-08-01 14:38:52,416 - ERROR - 错误信息: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:52,417 - ERROR - 详细堆栈跟踪:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:52,417 - ERROR - 失败批次信息:
2025-08-01 14:38:52,417 - ERROR -   - 批次索引: 79
2025-08-01 14:38:52,417 - ERROR -   - 批次大小: 2
2025-08-01 14:38:52,417 - ERROR -   - 无法获取批次详细信息
2025-08-01 14:38:52,418 - DEBUG - 批次 80 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:52,419 - DEBUG - 批次 80 数据类型: torch.float32
2025-08-01 14:38:52,419 - DEBUG - 批次 80 设备: cuda:0
2025-08-01 14:38:52,419 - DEBUG - 批次 80 数据范围: [0.000, 0.996]
2025-08-01 14:38:52,419 - WARNING - 批次 80 处理失败，重试 1/3
2025-08-01 14:38:52,419 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:52,419 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:52,419 - DEBUG - 已清理GPU缓存
2025-08-01 14:38:52,450 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:38:52,475 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:38:52,920 - DEBUG - 批次 80 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:52,921 - DEBUG - 批次 80 数据类型: torch.float32
2025-08-01 14:38:52,921 - DEBUG - 批次 80 设备: cuda:0
2025-08-01 14:38:52,921 - DEBUG - 批次 80 数据范围: [0.000, 0.996]
2025-08-01 14:38:52,921 - WARNING - 批次 80 处理失败，重试 2/3
2025-08-01 14:38:52,921 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:52,922 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:52,922 - DEBUG - 已清理GPU缓存
2025-08-01 14:38:53,423 - DEBUG - 批次 80 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:53,423 - DEBUG - 批次 80 数据类型: torch.float32
2025-08-01 14:38:53,424 - DEBUG - 批次 80 设备: cuda:0
2025-08-01 14:38:53,424 - DEBUG - 批次 80 数据范围: [0.000, 0.996]
2025-08-01 14:38:53,425 - ERROR - 批次 80 处理失败，已重试 3 次
2025-08-01 14:38:53,425 - ERROR - 错误类型: ValueError
2025-08-01 14:38:53,425 - ERROR - 错误信息: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:53,425 - ERROR - 详细堆栈跟踪:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:53,425 - ERROR - 失败批次信息:
2025-08-01 14:38:53,426 - ERROR -   - 批次索引: 80
2025-08-01 14:38:53,426 - ERROR -   - 批次大小: 2
2025-08-01 14:38:53,426 - ERROR -   - 无法获取批次详细信息
2025-08-01 14:38:53,427 - DEBUG - 批次 81 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:53,427 - DEBUG - 批次 81 数据类型: torch.float32
2025-08-01 14:38:53,427 - DEBUG - 批次 81 设备: cuda:0
2025-08-01 14:38:53,427 - DEBUG - 批次 81 数据范围: [0.000, 0.992]
2025-08-01 14:38:53,428 - WARNING - 批次 81 处理失败，重试 1/3
2025-08-01 14:38:53,428 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:53,428 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:53,428 - DEBUG - 已清理GPU缓存
2025-08-01 14:38:53,460 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:38:53,486 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:38:53,929 - DEBUG - 批次 81 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:53,929 - DEBUG - 批次 81 数据类型: torch.float32
2025-08-01 14:38:53,930 - DEBUG - 批次 81 设备: cuda:0
2025-08-01 14:38:53,930 - DEBUG - 批次 81 数据范围: [0.000, 0.992]
2025-08-01 14:38:53,931 - WARNING - 批次 81 处理失败，重试 2/3
2025-08-01 14:38:53,931 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:53,931 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:53,931 - DEBUG - 已清理GPU缓存
2025-08-01 14:38:54,432 - DEBUG - 批次 81 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:54,433 - DEBUG - 批次 81 数据类型: torch.float32
2025-08-01 14:38:54,433 - DEBUG - 批次 81 设备: cuda:0
2025-08-01 14:38:54,433 - DEBUG - 批次 81 数据范围: [0.000, 0.992]
2025-08-01 14:38:54,434 - ERROR - 批次 81 处理失败，已重试 3 次
2025-08-01 14:38:54,434 - ERROR - 错误类型: ValueError
2025-08-01 14:38:54,434 - ERROR - 错误信息: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:54,435 - ERROR - 详细堆栈跟踪:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:54,435 - ERROR - 失败批次信息:
2025-08-01 14:38:54,435 - ERROR -   - 批次索引: 81
2025-08-01 14:38:54,435 - ERROR -   - 批次大小: 2
2025-08-01 14:38:54,435 - ERROR -   - 无法获取批次详细信息
2025-08-01 14:38:54,436 - DEBUG - 批次 82 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:54,436 - DEBUG - 批次 82 数据类型: torch.float32
2025-08-01 14:38:54,436 - DEBUG - 批次 82 设备: cuda:0
2025-08-01 14:38:54,437 - DEBUG - 批次 82 数据范围: [0.000, 0.996]
2025-08-01 14:38:54,437 - WARNING - 批次 82 处理失败，重试 1/3
2025-08-01 14:38:54,437 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:54,437 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:54,437 - DEBUG - 已清理GPU缓存
2025-08-01 14:38:54,470 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:38:54,496 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:38:54,938 - DEBUG - 批次 82 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:54,939 - DEBUG - 批次 82 数据类型: torch.float32
2025-08-01 14:38:54,939 - DEBUG - 批次 82 设备: cuda:0
2025-08-01 14:38:54,939 - DEBUG - 批次 82 数据范围: [0.000, 0.996]
2025-08-01 14:38:54,940 - WARNING - 批次 82 处理失败，重试 2/3
2025-08-01 14:38:54,940 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:54,940 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:54,941 - DEBUG - 已清理GPU缓存
2025-08-01 14:38:55,442 - DEBUG - 批次 82 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:55,442 - DEBUG - 批次 82 数据类型: torch.float32
2025-08-01 14:38:55,442 - DEBUG - 批次 82 设备: cuda:0
2025-08-01 14:38:55,442 - DEBUG - 批次 82 数据范围: [0.000, 0.996]
2025-08-01 14:38:55,443 - ERROR - 批次 82 处理失败，已重试 3 次
2025-08-01 14:38:55,443 - ERROR - 错误类型: ValueError
2025-08-01 14:38:55,443 - ERROR - 错误信息: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:55,443 - ERROR - 详细堆栈跟踪:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:55,443 - ERROR - 失败批次信息:
2025-08-01 14:38:55,443 - ERROR -   - 批次索引: 82
2025-08-01 14:38:55,443 - ERROR -   - 批次大小: 2
2025-08-01 14:38:55,443 - ERROR -   - 无法获取批次详细信息
2025-08-01 14:38:55,444 - DEBUG - 批次 83 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:55,444 - DEBUG - 批次 83 数据类型: torch.float32
2025-08-01 14:38:55,444 - DEBUG - 批次 83 设备: cuda:0
2025-08-01 14:38:55,444 - DEBUG - 批次 83 数据范围: [0.000, 1.000]
2025-08-01 14:38:55,444 - WARNING - 批次 83 处理失败，重试 1/3
2025-08-01 14:38:55,445 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:55,445 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:55,445 - DEBUG - 已清理GPU缓存
2025-08-01 14:38:55,477 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:38:55,504 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:38:55,946 - DEBUG - 批次 83 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:55,946 - DEBUG - 批次 83 数据类型: torch.float32
2025-08-01 14:38:55,947 - DEBUG - 批次 83 设备: cuda:0
2025-08-01 14:38:55,947 - DEBUG - 批次 83 数据范围: [0.000, 1.000]
2025-08-01 14:38:55,948 - WARNING - 批次 83 处理失败，重试 2/3
2025-08-01 14:38:55,948 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:55,948 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:55,948 - DEBUG - 已清理GPU缓存
2025-08-01 14:38:56,450 - DEBUG - 批次 83 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:56,450 - DEBUG - 批次 83 数据类型: torch.float32
2025-08-01 14:38:56,450 - DEBUG - 批次 83 设备: cuda:0
2025-08-01 14:38:56,451 - DEBUG - 批次 83 数据范围: [0.000, 1.000]
2025-08-01 14:38:56,452 - ERROR - 批次 83 处理失败，已重试 3 次
2025-08-01 14:38:56,452 - ERROR - 错误类型: ValueError
2025-08-01 14:38:56,452 - ERROR - 错误信息: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:56,452 - ERROR - 详细堆栈跟踪:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:56,452 - ERROR - 失败批次信息:
2025-08-01 14:38:56,452 - ERROR -   - 批次索引: 83
2025-08-01 14:38:56,453 - ERROR -   - 批次大小: 2
2025-08-01 14:38:56,453 - ERROR -   - 无法获取批次详细信息
2025-08-01 14:38:56,454 - DEBUG - 批次 84 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:56,454 - DEBUG - 批次 84 数据类型: torch.float32
2025-08-01 14:38:56,454 - DEBUG - 批次 84 设备: cuda:0
2025-08-01 14:38:56,454 - DEBUG - 批次 84 数据范围: [0.000, 1.000]
2025-08-01 14:38:56,455 - WARNING - 批次 84 处理失败，重试 1/3
2025-08-01 14:38:56,455 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:56,455 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:56,455 - DEBUG - 已清理GPU缓存
2025-08-01 14:38:56,486 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:38:56,511 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:38:56,956 - DEBUG - 批次 84 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:56,956 - DEBUG - 批次 84 数据类型: torch.float32
2025-08-01 14:38:56,956 - DEBUG - 批次 84 设备: cuda:0
2025-08-01 14:38:56,957 - DEBUG - 批次 84 数据范围: [0.000, 1.000]
2025-08-01 14:38:56,958 - WARNING - 批次 84 处理失败，重试 2/3
2025-08-01 14:38:56,958 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:56,958 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:56,958 - DEBUG - 已清理GPU缓存
2025-08-01 14:38:57,459 - DEBUG - 批次 84 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:57,460 - DEBUG - 批次 84 数据类型: torch.float32
2025-08-01 14:38:57,460 - DEBUG - 批次 84 设备: cuda:0
2025-08-01 14:38:57,460 - DEBUG - 批次 84 数据范围: [0.000, 1.000]
2025-08-01 14:38:57,461 - ERROR - 批次 84 处理失败，已重试 3 次
2025-08-01 14:38:57,461 - ERROR - 错误类型: ValueError
2025-08-01 14:38:57,462 - ERROR - 错误信息: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:57,462 - ERROR - 详细堆栈跟踪:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:57,462 - ERROR - 失败批次信息:
2025-08-01 14:38:57,462 - ERROR -   - 批次索引: 84
2025-08-01 14:38:57,462 - ERROR -   - 批次大小: 2
2025-08-01 14:38:57,462 - ERROR -   - 无法获取批次详细信息
2025-08-01 14:38:57,463 - DEBUG - 批次 85 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:57,463 - DEBUG - 批次 85 数据类型: torch.float32
2025-08-01 14:38:57,463 - DEBUG - 批次 85 设备: cuda:0
2025-08-01 14:38:57,463 - DEBUG - 批次 85 数据范围: [0.000, 1.000]
2025-08-01 14:38:57,464 - WARNING - 批次 85 处理失败，重试 1/3
2025-08-01 14:38:57,464 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:57,464 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:57,464 - DEBUG - 已清理GPU缓存
2025-08-01 14:38:57,495 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:38:57,522 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:38:57,965 - DEBUG - 批次 85 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:57,965 - DEBUG - 批次 85 数据类型: torch.float32
2025-08-01 14:38:57,965 - DEBUG - 批次 85 设备: cuda:0
2025-08-01 14:38:57,966 - DEBUG - 批次 85 数据范围: [0.000, 1.000]
2025-08-01 14:38:57,966 - WARNING - 批次 85 处理失败，重试 2/3
2025-08-01 14:38:57,966 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:57,966 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:57,966 - DEBUG - 已清理GPU缓存
2025-08-01 14:38:58,468 - DEBUG - 批次 85 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:58,468 - DEBUG - 批次 85 数据类型: torch.float32
2025-08-01 14:38:58,468 - DEBUG - 批次 85 设备: cuda:0
2025-08-01 14:38:58,469 - DEBUG - 批次 85 数据范围: [0.000, 1.000]
2025-08-01 14:38:58,469 - ERROR - 批次 85 处理失败，已重试 3 次
2025-08-01 14:38:58,470 - ERROR - 错误类型: ValueError
2025-08-01 14:38:58,470 - ERROR - 错误信息: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:58,470 - ERROR - 详细堆栈跟踪:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:58,470 - ERROR - 失败批次信息:
2025-08-01 14:38:58,470 - ERROR -   - 批次索引: 85
2025-08-01 14:38:58,470 - ERROR -   - 批次大小: 2
2025-08-01 14:38:58,470 - ERROR -   - 无法获取批次详细信息
2025-08-01 14:38:58,471 - DEBUG - 批次 86 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:58,471 - DEBUG - 批次 86 数据类型: torch.float32
2025-08-01 14:38:58,471 - DEBUG - 批次 86 设备: cuda:0
2025-08-01 14:38:58,471 - DEBUG - 批次 86 数据范围: [0.000, 1.000]
2025-08-01 14:38:58,472 - WARNING - 批次 86 处理失败，重试 1/3
2025-08-01 14:38:58,472 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:58,472 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:58,472 - DEBUG - 已清理GPU缓存
2025-08-01 14:38:58,503 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:38:58,529 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:38:58,973 - DEBUG - 批次 86 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:58,973 - DEBUG - 批次 86 数据类型: torch.float32
2025-08-01 14:38:58,974 - DEBUG - 批次 86 设备: cuda:0
2025-08-01 14:38:58,974 - DEBUG - 批次 86 数据范围: [0.000, 1.000]
2025-08-01 14:38:58,975 - WARNING - 批次 86 处理失败，重试 2/3
2025-08-01 14:38:58,975 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:58,975 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:58,975 - DEBUG - 已清理GPU缓存
2025-08-01 14:38:59,477 - DEBUG - 批次 86 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:59,477 - DEBUG - 批次 86 数据类型: torch.float32
2025-08-01 14:38:59,477 - DEBUG - 批次 86 设备: cuda:0
2025-08-01 14:38:59,478 - DEBUG - 批次 86 数据范围: [0.000, 1.000]
2025-08-01 14:38:59,478 - ERROR - 批次 86 处理失败，已重试 3 次
2025-08-01 14:38:59,479 - ERROR - 错误类型: ValueError
2025-08-01 14:38:59,479 - ERROR - 错误信息: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:59,479 - ERROR - 详细堆栈跟踪:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:59,479 - ERROR - 失败批次信息:
2025-08-01 14:38:59,479 - ERROR -   - 批次索引: 86
2025-08-01 14:38:59,479 - ERROR -   - 批次大小: 2
2025-08-01 14:38:59,479 - ERROR -   - 无法获取批次详细信息
2025-08-01 14:38:59,480 - DEBUG - 批次 87 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:59,480 - DEBUG - 批次 87 数据类型: torch.float32
2025-08-01 14:38:59,480 - DEBUG - 批次 87 设备: cuda:0
2025-08-01 14:38:59,480 - DEBUG - 批次 87 数据范围: [0.000, 0.996]
2025-08-01 14:38:59,480 - WARNING - 批次 87 处理失败，重试 1/3
2025-08-01 14:38:59,480 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:59,480 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:59,481 - DEBUG - 已清理GPU缓存
2025-08-01 14:38:59,511 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:38:59,536 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:38:59,982 - DEBUG - 批次 87 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:59,982 - DEBUG - 批次 87 数据类型: torch.float32
2025-08-01 14:38:59,982 - DEBUG - 批次 87 设备: cuda:0
2025-08-01 14:38:59,983 - DEBUG - 批次 87 数据范围: [0.000, 0.996]
2025-08-01 14:38:59,983 - WARNING - 批次 87 处理失败，重试 2/3
2025-08-01 14:38:59,984 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:38:59,984 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:38:59,984 - DEBUG - 已清理GPU缓存
2025-08-01 14:39:00,485 - DEBUG - 批次 87 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:00,485 - DEBUG - 批次 87 数据类型: torch.float32
2025-08-01 14:39:00,485 - DEBUG - 批次 87 设备: cuda:0
2025-08-01 14:39:00,486 - DEBUG - 批次 87 数据范围: [0.000, 0.996]
2025-08-01 14:39:00,486 - ERROR - 批次 87 处理失败，已重试 3 次
2025-08-01 14:39:00,486 - ERROR - 错误类型: ValueError
2025-08-01 14:39:00,486 - ERROR - 错误信息: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:00,486 - ERROR - 详细堆栈跟踪:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:39:00,486 - ERROR - 失败批次信息:
2025-08-01 14:39:00,486 - ERROR -   - 批次索引: 87
2025-08-01 14:39:00,486 - ERROR -   - 批次大小: 2
2025-08-01 14:39:00,486 - ERROR -   - 无法获取批次详细信息
2025-08-01 14:39:00,487 - DEBUG - 批次 88 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:00,488 - DEBUG - 批次 88 数据类型: torch.float32
2025-08-01 14:39:00,488 - DEBUG - 批次 88 设备: cuda:0
2025-08-01 14:39:00,488 - DEBUG - 批次 88 数据范围: [0.000, 0.996]
2025-08-01 14:39:00,488 - WARNING - 批次 88 处理失败，重试 1/3
2025-08-01 14:39:00,488 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:00,488 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:39:00,488 - DEBUG - 已清理GPU缓存
2025-08-01 14:39:00,524 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:39:00,550 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:39:00,990 - DEBUG - 批次 88 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:00,990 - DEBUG - 批次 88 数据类型: torch.float32
2025-08-01 14:39:00,990 - DEBUG - 批次 88 设备: cuda:0
2025-08-01 14:39:00,991 - DEBUG - 批次 88 数据范围: [0.000, 0.996]
2025-08-01 14:39:00,991 - WARNING - 批次 88 处理失败，重试 2/3
2025-08-01 14:39:00,992 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:00,992 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:39:00,992 - DEBUG - 已清理GPU缓存
2025-08-01 14:39:01,493 - DEBUG - 批次 88 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:01,493 - DEBUG - 批次 88 数据类型: torch.float32
2025-08-01 14:39:01,494 - DEBUG - 批次 88 设备: cuda:0
2025-08-01 14:39:01,494 - DEBUG - 批次 88 数据范围: [0.000, 0.996]
2025-08-01 14:39:01,495 - ERROR - 批次 88 处理失败，已重试 3 次
2025-08-01 14:39:01,495 - ERROR - 错误类型: ValueError
2025-08-01 14:39:01,495 - ERROR - 错误信息: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:01,495 - ERROR - 详细堆栈跟踪:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:39:01,496 - ERROR - 失败批次信息:
2025-08-01 14:39:01,496 - ERROR -   - 批次索引: 88
2025-08-01 14:39:01,496 - ERROR -   - 批次大小: 2
2025-08-01 14:39:01,496 - ERROR -   - 无法获取批次详细信息
2025-08-01 14:39:01,497 - DEBUG - 批次 89 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:01,497 - DEBUG - 批次 89 数据类型: torch.float32
2025-08-01 14:39:01,497 - DEBUG - 批次 89 设备: cuda:0
2025-08-01 14:39:01,497 - DEBUG - 批次 89 数据范围: [0.000, 0.996]
2025-08-01 14:39:01,498 - WARNING - 批次 89 处理失败，重试 1/3
2025-08-01 14:39:01,498 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:01,498 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:39:01,498 - DEBUG - 已清理GPU缓存
2025-08-01 14:39:01,533 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:39:01,561 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:39:01,999 - DEBUG - 批次 89 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:02,000 - DEBUG - 批次 89 数据类型: torch.float32
2025-08-01 14:39:02,000 - DEBUG - 批次 89 设备: cuda:0
2025-08-01 14:39:02,000 - DEBUG - 批次 89 数据范围: [0.000, 0.996]
2025-08-01 14:39:02,001 - WARNING - 批次 89 处理失败，重试 2/3
2025-08-01 14:39:02,001 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:02,001 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:39:02,001 - DEBUG - 已清理GPU缓存
2025-08-01 14:39:02,502 - DEBUG - 批次 89 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:02,503 - DEBUG - 批次 89 数据类型: torch.float32
2025-08-01 14:39:02,503 - DEBUG - 批次 89 设备: cuda:0
2025-08-01 14:39:02,504 - DEBUG - 批次 89 数据范围: [0.000, 0.996]
2025-08-01 14:39:02,504 - ERROR - 批次 89 处理失败，已重试 3 次
2025-08-01 14:39:02,505 - ERROR - 错误类型: ValueError
2025-08-01 14:39:02,505 - ERROR - 错误信息: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:02,505 - ERROR - 详细堆栈跟踪:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:39:02,505 - ERROR - 失败批次信息:
2025-08-01 14:39:02,505 - ERROR -   - 批次索引: 89
2025-08-01 14:39:02,505 - ERROR -   - 批次大小: 2
2025-08-01 14:39:02,505 - ERROR -   - 无法获取批次详细信息
2025-08-01 14:39:02,506 - DEBUG - 批次 90 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:02,506 - DEBUG - 批次 90 数据类型: torch.float32
2025-08-01 14:39:02,506 - DEBUG - 批次 90 设备: cuda:0
2025-08-01 14:39:02,507 - DEBUG - 批次 90 数据范围: [0.000, 0.996]
2025-08-01 14:39:02,507 - WARNING - 批次 90 处理失败，重试 1/3
2025-08-01 14:39:02,507 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:02,507 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:39:02,507 - DEBUG - 已清理GPU缓存
2025-08-01 14:39:02,539 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:39:02,563 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:39:03,008 - DEBUG - 批次 90 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:03,009 - DEBUG - 批次 90 数据类型: torch.float32
2025-08-01 14:39:03,009 - DEBUG - 批次 90 设备: cuda:0
2025-08-01 14:39:03,009 - DEBUG - 批次 90 数据范围: [0.000, 0.996]
2025-08-01 14:39:03,010 - WARNING - 批次 90 处理失败，重试 2/3
2025-08-01 14:39:03,010 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:03,010 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:39:03,010 - DEBUG - 已清理GPU缓存
2025-08-01 14:39:03,512 - DEBUG - 批次 90 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:03,512 - DEBUG - 批次 90 数据类型: torch.float32
2025-08-01 14:39:03,512 - DEBUG - 批次 90 设备: cuda:0
2025-08-01 14:39:03,513 - DEBUG - 批次 90 数据范围: [0.000, 0.996]
2025-08-01 14:39:03,514 - ERROR - 批次 90 处理失败，已重试 3 次
2025-08-01 14:39:03,514 - ERROR - 错误类型: ValueError
2025-08-01 14:39:03,514 - ERROR - 错误信息: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:03,514 - ERROR - 详细堆栈跟踪:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:39:03,514 - ERROR - 失败批次信息:
2025-08-01 14:39:03,514 - ERROR -   - 批次索引: 90
2025-08-01 14:39:03,515 - ERROR -   - 批次大小: 2
2025-08-01 14:39:03,515 - ERROR -   - 无法获取批次详细信息
2025-08-01 14:39:03,515 - DEBUG - 批次 91 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:03,516 - DEBUG - 批次 91 数据类型: torch.float32
2025-08-01 14:39:03,516 - DEBUG - 批次 91 设备: cuda:0
2025-08-01 14:39:03,516 - DEBUG - 批次 91 数据范围: [0.000, 0.996]
2025-08-01 14:39:03,516 - WARNING - 批次 91 处理失败，重试 1/3
2025-08-01 14:39:03,516 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:03,516 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:39:03,516 - DEBUG - 已清理GPU缓存
2025-08-01 14:39:03,548 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:39:03,573 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:39:04,018 - DEBUG - 批次 91 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:04,018 - DEBUG - 批次 91 数据类型: torch.float32
2025-08-01 14:39:04,018 - DEBUG - 批次 91 设备: cuda:0
2025-08-01 14:39:04,019 - DEBUG - 批次 91 数据范围: [0.000, 0.996]
2025-08-01 14:39:04,019 - WARNING - 批次 91 处理失败，重试 2/3
2025-08-01 14:39:04,020 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:04,020 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:39:04,020 - DEBUG - 已清理GPU缓存
2025-08-01 14:39:04,521 - DEBUG - 批次 91 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:04,522 - DEBUG - 批次 91 数据类型: torch.float32
2025-08-01 14:39:04,522 - DEBUG - 批次 91 设备: cuda:0
2025-08-01 14:39:04,522 - DEBUG - 批次 91 数据范围: [0.000, 0.996]
2025-08-01 14:39:04,523 - ERROR - 批次 91 处理失败，已重试 3 次
2025-08-01 14:39:04,523 - ERROR - 错误类型: ValueError
2025-08-01 14:39:04,523 - ERROR - 错误信息: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:04,523 - ERROR - 详细堆栈跟踪:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:39:04,523 - ERROR - 失败批次信息:
2025-08-01 14:39:04,523 - ERROR -   - 批次索引: 91
2025-08-01 14:39:04,524 - ERROR -   - 批次大小: 2
2025-08-01 14:39:04,524 - ERROR -   - 无法获取批次详细信息
2025-08-01 14:39:04,524 - DEBUG - 批次 92 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:04,525 - DEBUG - 批次 92 数据类型: torch.float32
2025-08-01 14:39:04,525 - DEBUG - 批次 92 设备: cuda:0
2025-08-01 14:39:04,525 - DEBUG - 批次 92 数据范围: [0.000, 0.992]
2025-08-01 14:39:04,525 - WARNING - 批次 92 处理失败，重试 1/3
2025-08-01 14:39:04,525 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:04,525 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:39:04,525 - DEBUG - 已清理GPU缓存
2025-08-01 14:39:04,556 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:39:04,581 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:39:05,027 - DEBUG - 批次 92 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:05,027 - DEBUG - 批次 92 数据类型: torch.float32
2025-08-01 14:39:05,027 - DEBUG - 批次 92 设备: cuda:0
2025-08-01 14:39:05,028 - DEBUG - 批次 92 数据范围: [0.000, 0.992]
2025-08-01 14:39:05,028 - WARNING - 批次 92 处理失败，重试 2/3
2025-08-01 14:39:05,028 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:05,028 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:39:05,029 - DEBUG - 已清理GPU缓存
2025-08-01 14:39:05,530 - DEBUG - 批次 92 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:05,530 - DEBUG - 批次 92 数据类型: torch.float32
2025-08-01 14:39:05,531 - DEBUG - 批次 92 设备: cuda:0
2025-08-01 14:39:05,531 - DEBUG - 批次 92 数据范围: [0.000, 0.992]
2025-08-01 14:39:05,532 - ERROR - 批次 92 处理失败，已重试 3 次
2025-08-01 14:39:05,532 - ERROR - 错误类型: ValueError
2025-08-01 14:39:05,532 - ERROR - 错误信息: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:05,532 - ERROR - 详细堆栈跟踪:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:39:05,532 - ERROR - 失败批次信息:
2025-08-01 14:39:05,532 - ERROR -   - 批次索引: 92
2025-08-01 14:39:05,532 - ERROR -   - 批次大小: 2
2025-08-01 14:39:05,532 - ERROR -   - 无法获取批次详细信息
2025-08-01 14:39:05,533 - DEBUG - 批次 93 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:05,533 - DEBUG - 批次 93 数据类型: torch.float32
2025-08-01 14:39:05,533 - DEBUG - 批次 93 设备: cuda:0
2025-08-01 14:39:05,534 - DEBUG - 批次 93 数据范围: [0.000, 0.992]
2025-08-01 14:39:05,534 - WARNING - 批次 93 处理失败，重试 1/3
2025-08-01 14:39:05,534 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:05,534 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:39:05,534 - DEBUG - 已清理GPU缓存
2025-08-01 14:39:05,565 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:39:05,591 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:39:06,036 - DEBUG - 批次 93 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:06,036 - DEBUG - 批次 93 数据类型: torch.float32
2025-08-01 14:39:06,036 - DEBUG - 批次 93 设备: cuda:0
2025-08-01 14:39:06,037 - DEBUG - 批次 93 数据范围: [0.000, 0.992]
2025-08-01 14:39:06,037 - WARNING - 批次 93 处理失败，重试 2/3
2025-08-01 14:39:06,038 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:06,038 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:39:06,038 - DEBUG - 已清理GPU缓存
2025-08-01 14:39:06,539 - DEBUG - 批次 93 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:06,540 - DEBUG - 批次 93 数据类型: torch.float32
2025-08-01 14:39:06,540 - DEBUG - 批次 93 设备: cuda:0
2025-08-01 14:39:06,540 - DEBUG - 批次 93 数据范围: [0.000, 0.992]
2025-08-01 14:39:06,541 - ERROR - 批次 93 处理失败，已重试 3 次
2025-08-01 14:39:06,541 - ERROR - 错误类型: ValueError
2025-08-01 14:39:06,541 - ERROR - 错误信息: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:06,542 - ERROR - 详细堆栈跟踪:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:39:06,542 - ERROR - 失败批次信息:
2025-08-01 14:39:06,542 - ERROR -   - 批次索引: 93
2025-08-01 14:39:06,542 - ERROR -   - 批次大小: 2
2025-08-01 14:39:06,542 - ERROR -   - 无法获取批次详细信息
2025-08-01 14:39:06,543 - DEBUG - 批次 94 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:06,543 - DEBUG - 批次 94 数据类型: torch.float32
2025-08-01 14:39:06,544 - DEBUG - 批次 94 设备: cuda:0
2025-08-01 14:39:06,544 - DEBUG - 批次 94 数据范围: [0.000, 0.996]
2025-08-01 14:39:06,545 - WARNING - 批次 94 处理失败，重试 1/3
2025-08-01 14:39:06,545 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:06,545 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:39:06,545 - DEBUG - 已清理GPU缓存
2025-08-01 14:39:06,575 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:39:06,601 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:39:07,046 - DEBUG - 批次 94 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:07,047 - DEBUG - 批次 94 数据类型: torch.float32
2025-08-01 14:39:07,047 - DEBUG - 批次 94 设备: cuda:0
2025-08-01 14:39:07,047 - DEBUG - 批次 94 数据范围: [0.000, 0.996]
2025-08-01 14:39:07,048 - WARNING - 批次 94 处理失败，重试 2/3
2025-08-01 14:39:07,048 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:07,049 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:39:07,049 - DEBUG - 已清理GPU缓存
2025-08-01 14:39:07,550 - DEBUG - 批次 94 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:07,550 - DEBUG - 批次 94 数据类型: torch.float32
2025-08-01 14:39:07,551 - DEBUG - 批次 94 设备: cuda:0
2025-08-01 14:39:07,551 - DEBUG - 批次 94 数据范围: [0.000, 0.996]
2025-08-01 14:39:07,552 - ERROR - 批次 94 处理失败，已重试 3 次
2025-08-01 14:39:07,552 - ERROR - 错误类型: ValueError
2025-08-01 14:39:07,552 - ERROR - 错误信息: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:07,552 - ERROR - 详细堆栈跟踪:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:39:07,552 - ERROR - 失败批次信息:
2025-08-01 14:39:07,552 - ERROR -   - 批次索引: 94
2025-08-01 14:39:07,553 - ERROR -   - 批次大小: 2
2025-08-01 14:39:07,553 - ERROR -   - 无法获取批次详细信息
2025-08-01 14:39:07,554 - DEBUG - 批次 95 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:07,554 - DEBUG - 批次 95 数据类型: torch.float32
2025-08-01 14:39:07,555 - DEBUG - 批次 95 设备: cuda:0
2025-08-01 14:39:07,555 - DEBUG - 批次 95 数据范围: [0.000, 1.000]
2025-08-01 14:39:07,556 - WARNING - 批次 95 处理失败，重试 1/3
2025-08-01 14:39:07,556 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:07,556 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:39:07,556 - DEBUG - 已清理GPU缓存
2025-08-01 14:39:07,583 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:39:07,610 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:39:08,058 - DEBUG - 批次 95 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:08,058 - DEBUG - 批次 95 数据类型: torch.float32
2025-08-01 14:39:08,058 - DEBUG - 批次 95 设备: cuda:0
2025-08-01 14:39:08,059 - DEBUG - 批次 95 数据范围: [0.000, 1.000]
2025-08-01 14:39:08,059 - WARNING - 批次 95 处理失败，重试 2/3
2025-08-01 14:39:08,060 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:08,060 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:39:08,060 - DEBUG - 已清理GPU缓存
2025-08-01 14:39:08,561 - DEBUG - 批次 95 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:08,562 - DEBUG - 批次 95 数据类型: torch.float32
2025-08-01 14:39:08,562 - DEBUG - 批次 95 设备: cuda:0
2025-08-01 14:39:08,562 - DEBUG - 批次 95 数据范围: [0.000, 1.000]
2025-08-01 14:39:08,563 - ERROR - 批次 95 处理失败，已重试 3 次
2025-08-01 14:39:08,563 - ERROR - 错误类型: ValueError
2025-08-01 14:39:08,563 - ERROR - 错误信息: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:08,564 - ERROR - 详细堆栈跟踪:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:39:08,564 - ERROR - 失败批次信息:
2025-08-01 14:39:08,564 - ERROR -   - 批次索引: 95
2025-08-01 14:39:08,564 - ERROR -   - 批次大小: 2
2025-08-01 14:39:08,564 - ERROR -   - 无法获取批次详细信息
2025-08-01 14:39:08,566 - DEBUG - 批次 96 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:08,566 - DEBUG - 批次 96 数据类型: torch.float32
2025-08-01 14:39:08,566 - DEBUG - 批次 96 设备: cuda:0
2025-08-01 14:39:08,566 - DEBUG - 批次 96 数据范围: [0.000, 1.000]
2025-08-01 14:39:08,567 - WARNING - 批次 96 处理失败，重试 1/3
2025-08-01 14:39:08,567 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:08,567 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:39:08,568 - DEBUG - 已清理GPU缓存
2025-08-01 14:39:08,595 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:39:08,620 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:39:09,069 - DEBUG - 批次 96 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:09,069 - DEBUG - 批次 96 数据类型: torch.float32
2025-08-01 14:39:09,070 - DEBUG - 批次 96 设备: cuda:0
2025-08-01 14:39:09,070 - DEBUG - 批次 96 数据范围: [0.000, 1.000]
2025-08-01 14:39:09,071 - WARNING - 批次 96 处理失败，重试 2/3
2025-08-01 14:39:09,071 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:09,071 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:39:09,071 - DEBUG - 已清理GPU缓存
2025-08-01 14:39:09,573 - DEBUG - 批次 96 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:09,573 - DEBUG - 批次 96 数据类型: torch.float32
2025-08-01 14:39:09,573 - DEBUG - 批次 96 设备: cuda:0
2025-08-01 14:39:09,574 - DEBUG - 批次 96 数据范围: [0.000, 1.000]
2025-08-01 14:39:09,575 - ERROR - 批次 96 处理失败，已重试 3 次
2025-08-01 14:39:09,575 - ERROR - 错误类型: ValueError
2025-08-01 14:39:09,575 - ERROR - 错误信息: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:09,575 - ERROR - 详细堆栈跟踪:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:39:09,575 - ERROR - 失败批次信息:
2025-08-01 14:39:09,575 - ERROR -   - 批次索引: 96
2025-08-01 14:39:09,576 - ERROR -   - 批次大小: 2
2025-08-01 14:39:09,576 - ERROR -   - 无法获取批次详细信息
2025-08-01 14:39:09,577 - DEBUG - 批次 97 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:09,577 - DEBUG - 批次 97 数据类型: torch.float32
2025-08-01 14:39:09,577 - DEBUG - 批次 97 设备: cuda:0
2025-08-01 14:39:09,577 - DEBUG - 批次 97 数据范围: [0.000, 1.000]
2025-08-01 14:39:09,578 - WARNING - 批次 97 处理失败，重试 1/3
2025-08-01 14:39:09,578 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:09,578 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:39:09,578 - DEBUG - 已清理GPU缓存
2025-08-01 14:39:09,610 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:39:09,636 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:39:10,079 - DEBUG - 批次 97 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:10,079 - DEBUG - 批次 97 数据类型: torch.float32
2025-08-01 14:39:10,080 - DEBUG - 批次 97 设备: cuda:0
2025-08-01 14:39:10,080 - DEBUG - 批次 97 数据范围: [0.000, 1.000]
2025-08-01 14:39:10,081 - WARNING - 批次 97 处理失败，重试 2/3
2025-08-01 14:39:10,081 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:10,081 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:39:10,081 - DEBUG - 已清理GPU缓存
2025-08-01 14:39:10,582 - DEBUG - 批次 97 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:10,583 - DEBUG - 批次 97 数据类型: torch.float32
2025-08-01 14:39:10,583 - DEBUG - 批次 97 设备: cuda:0
2025-08-01 14:39:10,583 - DEBUG - 批次 97 数据范围: [0.000, 1.000]
2025-08-01 14:39:10,584 - ERROR - 批次 97 处理失败，已重试 3 次
2025-08-01 14:39:10,585 - ERROR - 错误类型: ValueError
2025-08-01 14:39:10,585 - ERROR - 错误信息: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:10,585 - ERROR - 详细堆栈跟踪:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:39:10,585 - ERROR - 失败批次信息:
2025-08-01 14:39:10,585 - ERROR -   - 批次索引: 97
2025-08-01 14:39:10,585 - ERROR -   - 批次大小: 2
2025-08-01 14:39:10,585 - ERROR -   - 无法获取批次详细信息
2025-08-01 14:39:10,586 - DEBUG - 批次 98 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:10,586 - DEBUG - 批次 98 数据类型: torch.float32
2025-08-01 14:39:10,587 - DEBUG - 批次 98 设备: cuda:0
2025-08-01 14:39:10,587 - DEBUG - 批次 98 数据范围: [0.000, 1.000]
2025-08-01 14:39:10,587 - WARNING - 批次 98 处理失败，重试 1/3
2025-08-01 14:39:10,587 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:10,587 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:39:10,587 - DEBUG - 已清理GPU缓存
2025-08-01 14:39:10,619 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:39:10,644 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:39:11,089 - DEBUG - 批次 98 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:11,089 - DEBUG - 批次 98 数据类型: torch.float32
2025-08-01 14:39:11,089 - DEBUG - 批次 98 设备: cuda:0
2025-08-01 14:39:11,090 - DEBUG - 批次 98 数据范围: [0.000, 1.000]
2025-08-01 14:39:11,090 - WARNING - 批次 98 处理失败，重试 2/3
2025-08-01 14:39:11,090 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:11,091 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:39:11,091 - DEBUG - 已清理GPU缓存
2025-08-01 14:39:11,592 - DEBUG - 批次 98 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:11,592 - DEBUG - 批次 98 数据类型: torch.float32
2025-08-01 14:39:11,592 - DEBUG - 批次 98 设备: cuda:0
2025-08-01 14:39:11,593 - DEBUG - 批次 98 数据范围: [0.000, 1.000]
2025-08-01 14:39:11,594 - ERROR - 批次 98 处理失败，已重试 3 次
2025-08-01 14:39:11,594 - ERROR - 错误类型: ValueError
2025-08-01 14:39:11,594 - ERROR - 错误信息: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:11,594 - ERROR - 详细堆栈跟踪:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:39:11,594 - ERROR - 失败批次信息:
2025-08-01 14:39:11,594 - ERROR -   - 批次索引: 98
2025-08-01 14:39:11,594 - ERROR -   - 批次大小: 2
2025-08-01 14:39:11,594 - ERROR -   - 无法获取批次详细信息
2025-08-01 14:39:11,595 - DEBUG - 批次 99 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:11,595 - DEBUG - 批次 99 数据类型: torch.float32
2025-08-01 14:39:11,595 - DEBUG - 批次 99 设备: cuda:0
2025-08-01 14:39:11,596 - DEBUG - 批次 99 数据范围: [0.000, 0.996]
2025-08-01 14:39:11,596 - WARNING - 批次 99 处理失败，重试 1/3
2025-08-01 14:39:11,596 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:11,596 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:39:11,596 - DEBUG - 已清理GPU缓存
2025-08-01 14:39:11,628 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:39:11,653 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:39:12,097 - DEBUG - 批次 99 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:12,098 - DEBUG - 批次 99 数据类型: torch.float32
2025-08-01 14:39:12,098 - DEBUG - 批次 99 设备: cuda:0
2025-08-01 14:39:12,098 - DEBUG - 批次 99 数据范围: [0.000, 0.996]
2025-08-01 14:39:12,099 - WARNING - 批次 99 处理失败，重试 2/3
2025-08-01 14:39:12,099 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:12,099 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:39:12,099 - DEBUG - 已清理GPU缓存
2025-08-01 14:39:12,601 - DEBUG - 批次 99 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:12,601 - DEBUG - 批次 99 数据类型: torch.float32
2025-08-01 14:39:12,601 - DEBUG - 批次 99 设备: cuda:0
2025-08-01 14:39:12,602 - DEBUG - 批次 99 数据范围: [0.000, 0.996]
2025-08-01 14:39:12,602 - ERROR - 批次 99 处理失败，已重试 3 次
2025-08-01 14:39:12,603 - ERROR - 错误类型: ValueError
2025-08-01 14:39:12,603 - ERROR - 错误信息: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:12,603 - ERROR - 详细堆栈跟踪:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:39:12,603 - ERROR - 失败批次信息:
2025-08-01 14:39:12,603 - ERROR -   - 批次索引: 99
2025-08-01 14:39:12,603 - ERROR -   - 批次大小: 2
2025-08-01 14:39:12,603 - ERROR -   - 无法获取批次详细信息
2025-08-01 14:39:12,603 - INFO - 已处理 100/822 批次，成功样本: 0，失败批次: 100
2025-08-01 14:39:12,604 - DEBUG - 批次 100 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:12,604 - DEBUG - 批次 100 数据类型: torch.float32
2025-08-01 14:39:12,604 - DEBUG - 批次 100 设备: cuda:0
2025-08-01 14:39:12,604 - DEBUG - 批次 100 数据范围: [0.000, 0.992]
2025-08-01 14:39:12,605 - WARNING - 批次 100 处理失败，重试 1/3
2025-08-01 14:39:12,605 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:12,605 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:39:12,605 - DEBUG - 已清理GPU缓存
2025-08-01 14:39:12,637 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:39:12,664 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:39:13,106 - DEBUG - 批次 100 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:13,106 - DEBUG - 批次 100 数据类型: torch.float32
2025-08-01 14:39:13,107 - DEBUG - 批次 100 设备: cuda:0
2025-08-01 14:39:13,107 - DEBUG - 批次 100 数据范围: [0.000, 0.992]
2025-08-01 14:39:13,108 - WARNING - 批次 100 处理失败，重试 2/3
2025-08-01 14:39:13,108 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:13,108 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:39:13,108 - DEBUG - 已清理GPU缓存
2025-08-01 14:39:13,609 - DEBUG - 批次 100 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:13,610 - DEBUG - 批次 100 数据类型: torch.float32
2025-08-01 14:39:13,610 - DEBUG - 批次 100 设备: cuda:0
2025-08-01 14:39:13,610 - DEBUG - 批次 100 数据范围: [0.000, 0.992]
2025-08-01 14:39:13,611 - ERROR - 批次 100 处理失败，已重试 3 次
2025-08-01 14:39:13,611 - ERROR - 错误类型: ValueError
2025-08-01 14:39:13,612 - ERROR - 错误信息: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:13,612 - ERROR - 详细堆栈跟踪:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:39:13,612 - ERROR - 失败批次信息:
2025-08-01 14:39:13,612 - ERROR -   - 批次索引: 100
2025-08-01 14:39:13,612 - ERROR -   - 批次大小: 2
2025-08-01 14:39:13,612 - ERROR -   - 无法获取批次详细信息
2025-08-01 14:39:13,613 - DEBUG - 批次 101 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:13,614 - DEBUG - 批次 101 数据类型: torch.float32
2025-08-01 14:39:13,614 - DEBUG - 批次 101 设备: cuda:0
2025-08-01 14:39:13,614 - DEBUG - 批次 101 数据范围: [0.000, 0.996]
2025-08-01 14:39:13,614 - WARNING - 批次 101 处理失败，重试 1/3
2025-08-01 14:39:13,614 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:13,614 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:39:13,614 - DEBUG - 已清理GPU缓存
2025-08-01 14:39:13,648 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:39:13,677 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:39:14,116 - DEBUG - 批次 101 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:14,116 - DEBUG - 批次 101 数据类型: torch.float32
2025-08-01 14:39:14,116 - DEBUG - 批次 101 设备: cuda:0
2025-08-01 14:39:14,117 - DEBUG - 批次 101 数据范围: [0.000, 0.996]
2025-08-01 14:39:14,117 - WARNING - 批次 101 处理失败，重试 2/3
2025-08-01 14:39:14,117 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:14,117 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:39:14,117 - DEBUG - 已清理GPU缓存
2025-08-01 14:39:14,619 - DEBUG - 批次 101 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:14,619 - DEBUG - 批次 101 数据类型: torch.float32
2025-08-01 14:39:14,619 - DEBUG - 批次 101 设备: cuda:0
2025-08-01 14:39:14,620 - DEBUG - 批次 101 数据范围: [0.000, 0.996]
2025-08-01 14:39:14,621 - ERROR - 批次 101 处理失败，已重试 3 次
2025-08-01 14:39:14,621 - ERROR - 错误类型: ValueError
2025-08-01 14:39:14,621 - ERROR - 错误信息: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:14,621 - ERROR - 详细堆栈跟踪:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:39:14,621 - ERROR - 失败批次信息:
2025-08-01 14:39:14,621 - ERROR -   - 批次索引: 101
2025-08-01 14:39:14,621 - ERROR -   - 批次大小: 2
2025-08-01 14:39:14,621 - ERROR -   - 无法获取批次详细信息
2025-08-01 14:39:14,622 - DEBUG - 批次 102 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:14,627 - DEBUG - 批次 102 数据类型: torch.float32
2025-08-01 14:39:14,627 - DEBUG - 批次 102 设备: cuda:0
2025-08-01 14:39:14,628 - DEBUG - 批次 102 数据范围: [0.000, 1.000]
2025-08-01 14:39:14,628 - WARNING - 批次 102 处理失败，重试 1/3
2025-08-01 14:39:14,628 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:14,628 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:39:14,628 - DEBUG - 已清理GPU缓存
2025-08-01 14:39:14,653 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:39:14,678 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:39:15,129 - DEBUG - 批次 102 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:15,130 - DEBUG - 批次 102 数据类型: torch.float32
2025-08-01 14:39:15,130 - DEBUG - 批次 102 设备: cuda:0
2025-08-01 14:39:15,130 - DEBUG - 批次 102 数据范围: [0.000, 1.000]
2025-08-01 14:39:15,131 - WARNING - 批次 102 处理失败，重试 2/3
2025-08-01 14:39:15,131 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:15,132 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:39:15,132 - DEBUG - 已清理GPU缓存
2025-08-01 14:39:15,633 - DEBUG - 批次 102 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:15,634 - DEBUG - 批次 102 数据类型: torch.float32
2025-08-01 14:39:15,634 - DEBUG - 批次 102 设备: cuda:0
2025-08-01 14:39:15,634 - DEBUG - 批次 102 数据范围: [0.000, 1.000]
2025-08-01 14:39:15,635 - ERROR - 批次 102 处理失败，已重试 3 次
2025-08-01 14:39:15,635 - ERROR - 错误类型: ValueError
2025-08-01 14:39:15,635 - ERROR - 错误信息: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:15,636 - ERROR - 详细堆栈跟踪:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:39:15,636 - ERROR - 失败批次信息:
2025-08-01 14:39:15,636 - ERROR -   - 批次索引: 102
2025-08-01 14:39:15,636 - ERROR -   - 批次大小: 2
2025-08-01 14:39:15,636 - ERROR -   - 无法获取批次详细信息
2025-08-01 14:39:15,638 - DEBUG - 批次 103 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:15,638 - DEBUG - 批次 103 数据类型: torch.float32
2025-08-01 14:39:15,638 - DEBUG - 批次 103 设备: cuda:0
2025-08-01 14:39:15,638 - DEBUG - 批次 103 数据范围: [0.000, 1.000]
2025-08-01 14:39:15,639 - WARNING - 批次 103 处理失败，重试 1/3
2025-08-01 14:39:15,639 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:15,640 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:39:15,641 - DEBUG - 已清理GPU缓存
2025-08-01 14:39:15,666 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:39:15,691 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:39:16,143 - DEBUG - 批次 103 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:16,143 - DEBUG - 批次 103 数据类型: torch.float32
2025-08-01 14:39:16,143 - DEBUG - 批次 103 设备: cuda:0
2025-08-01 14:39:16,144 - DEBUG - 批次 103 数据范围: [0.000, 1.000]
2025-08-01 14:39:16,145 - WARNING - 批次 103 处理失败，重试 2/3
2025-08-01 14:39:16,145 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:16,145 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:39:16,145 - DEBUG - 已清理GPU缓存
2025-08-01 14:39:16,647 - DEBUG - 批次 103 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:16,647 - DEBUG - 批次 103 数据类型: torch.float32
2025-08-01 14:39:16,647 - DEBUG - 批次 103 设备: cuda:0
2025-08-01 14:39:16,648 - DEBUG - 批次 103 数据范围: [0.000, 1.000]
2025-08-01 14:39:16,649 - ERROR - 批次 103 处理失败，已重试 3 次
2025-08-01 14:39:16,649 - ERROR - 错误类型: ValueError
2025-08-01 14:39:16,649 - ERROR - 错误信息: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:16,649 - ERROR - 详细堆栈跟踪:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:39:16,649 - ERROR - 失败批次信息:
2025-08-01 14:39:16,649 - ERROR -   - 批次索引: 103
2025-08-01 14:39:16,649 - ERROR -   - 批次大小: 2
2025-08-01 14:39:16,650 - ERROR -   - 无法获取批次详细信息
2025-08-01 14:39:16,651 - DEBUG - 批次 104 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:16,651 - DEBUG - 批次 104 数据类型: torch.float32
2025-08-01 14:39:16,651 - DEBUG - 批次 104 设备: cuda:0
2025-08-01 14:39:16,651 - DEBUG - 批次 104 数据范围: [0.000, 1.000]
2025-08-01 14:39:16,652 - WARNING - 批次 104 处理失败，重试 1/3
2025-08-01 14:39:16,652 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:16,652 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:39:16,652 - DEBUG - 已清理GPU缓存
2025-08-01 14:39:16,682 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:39:16,712 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:39:17,153 - DEBUG - 批次 104 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:17,153 - DEBUG - 批次 104 数据类型: torch.float32
2025-08-01 14:39:17,154 - DEBUG - 批次 104 设备: cuda:0
2025-08-01 14:39:17,154 - DEBUG - 批次 104 数据范围: [0.000, 1.000]
2025-08-01 14:39:17,155 - WARNING - 批次 104 处理失败，重试 2/3
2025-08-01 14:39:17,155 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:17,155 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:39:17,155 - DEBUG - 已清理GPU缓存
2025-08-01 14:39:17,657 - DEBUG - 批次 104 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:17,657 - DEBUG - 批次 104 数据类型: torch.float32
2025-08-01 14:39:17,657 - DEBUG - 批次 104 设备: cuda:0
2025-08-01 14:39:17,658 - DEBUG - 批次 104 数据范围: [0.000, 1.000]
2025-08-01 14:39:17,659 - ERROR - 批次 104 处理失败，已重试 3 次
2025-08-01 14:39:17,659 - ERROR - 错误类型: ValueError
2025-08-01 14:39:17,659 - ERROR - 错误信息: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:17,659 - ERROR - 详细堆栈跟踪:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:39:17,659 - ERROR - 失败批次信息:
2025-08-01 14:39:17,659 - ERROR -   - 批次索引: 104
2025-08-01 14:39:17,659 - ERROR -   - 批次大小: 2
2025-08-01 14:39:17,660 - ERROR -   - 无法获取批次详细信息
2025-08-01 14:39:17,661 - DEBUG - 批次 105 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:17,661 - DEBUG - 批次 105 数据类型: torch.float32
2025-08-01 14:39:17,661 - DEBUG - 批次 105 设备: cuda:0
2025-08-01 14:39:17,662 - DEBUG - 批次 105 数据范围: [0.000, 1.000]
2025-08-01 14:39:17,662 - WARNING - 批次 105 处理失败，重试 1/3
2025-08-01 14:39:17,662 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:17,662 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:39:17,662 - DEBUG - 已清理GPU缓存
2025-08-01 14:39:17,694 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:39:17,721 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:39:18,163 - DEBUG - 批次 105 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:18,164 - DEBUG - 批次 105 数据类型: torch.float32
2025-08-01 14:39:18,164 - DEBUG - 批次 105 设备: cuda:0
2025-08-01 14:39:18,165 - DEBUG - 批次 105 数据范围: [0.000, 1.000]
2025-08-01 14:39:18,165 - WARNING - 批次 105 处理失败，重试 2/3
2025-08-01 14:39:18,166 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:18,166 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:39:18,166 - DEBUG - 已清理GPU缓存
2025-08-01 14:39:18,667 - DEBUG - 批次 105 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:18,668 - DEBUG - 批次 105 数据类型: torch.float32
2025-08-01 14:39:18,668 - DEBUG - 批次 105 设备: cuda:0
2025-08-01 14:39:18,668 - DEBUG - 批次 105 数据范围: [0.000, 1.000]
2025-08-01 14:39:18,669 - ERROR - 批次 105 处理失败，已重试 3 次
2025-08-01 14:39:18,669 - ERROR - 错误类型: ValueError
2025-08-01 14:39:18,669 - ERROR - 错误信息: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:18,670 - ERROR - 详细堆栈跟踪:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:39:18,670 - ERROR - 失败批次信息:
2025-08-01 14:39:18,670 - ERROR -   - 批次索引: 105
2025-08-01 14:39:18,670 - ERROR -   - 批次大小: 2
2025-08-01 14:39:18,670 - ERROR -   - 无法获取批次详细信息
2025-08-01 14:39:18,672 - DEBUG - 批次 106 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:18,672 - DEBUG - 批次 106 数据类型: torch.float32
2025-08-01 14:39:18,672 - DEBUG - 批次 106 设备: cuda:0
2025-08-01 14:39:18,672 - DEBUG - 批次 106 数据范围: [0.000, 1.000]
2025-08-01 14:39:18,673 - WARNING - 批次 106 处理失败，重试 1/3
2025-08-01 14:39:18,673 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:18,673 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:39:18,673 - DEBUG - 已清理GPU缓存
2025-08-01 14:39:18,710 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:39:18,737 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:39:19,174 - DEBUG - 批次 106 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:19,175 - DEBUG - 批次 106 数据类型: torch.float32
2025-08-01 14:39:19,175 - DEBUG - 批次 106 设备: cuda:0
2025-08-01 14:39:19,175 - DEBUG - 批次 106 数据范围: [0.000, 1.000]
2025-08-01 14:39:19,176 - WARNING - 批次 106 处理失败，重试 2/3
2025-08-01 14:39:19,176 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:19,176 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:39:19,177 - DEBUG - 已清理GPU缓存
2025-08-01 14:39:19,678 - DEBUG - 批次 106 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:19,678 - DEBUG - 批次 106 数据类型: torch.float32
2025-08-01 14:39:19,679 - DEBUG - 批次 106 设备: cuda:0
2025-08-01 14:39:19,679 - DEBUG - 批次 106 数据范围: [0.000, 1.000]
2025-08-01 14:39:19,680 - ERROR - 批次 106 处理失败，已重试 3 次
2025-08-01 14:39:19,680 - ERROR - 错误类型: ValueError
2025-08-01 14:39:19,680 - ERROR - 错误信息: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:19,680 - ERROR - 详细堆栈跟踪:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:39:19,681 - ERROR - 失败批次信息:
2025-08-01 14:39:19,681 - ERROR -   - 批次索引: 106
2025-08-01 14:39:19,681 - ERROR -   - 批次大小: 2
2025-08-01 14:39:19,681 - ERROR -   - 无法获取批次详细信息
2025-08-01 14:39:19,682 - DEBUG - 批次 107 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:19,682 - DEBUG - 批次 107 数据类型: torch.float32
2025-08-01 14:39:19,682 - DEBUG - 批次 107 设备: cuda:0
2025-08-01 14:39:19,682 - DEBUG - 批次 107 数据范围: [0.000, 1.000]
2025-08-01 14:39:19,682 - WARNING - 批次 107 处理失败，重试 1/3
2025-08-01 14:39:19,683 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:19,683 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:39:19,683 - DEBUG - 已清理GPU缓存
2025-08-01 14:39:19,715 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:39:19,742 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:39:20,184 - DEBUG - 批次 107 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:20,184 - DEBUG - 批次 107 数据类型: torch.float32
2025-08-01 14:39:20,185 - DEBUG - 批次 107 设备: cuda:0
2025-08-01 14:39:20,185 - DEBUG - 批次 107 数据范围: [0.000, 1.000]
2025-08-01 14:39:20,186 - WARNING - 批次 107 处理失败，重试 2/3
2025-08-01 14:39:20,186 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:20,186 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:39:20,186 - DEBUG - 已清理GPU缓存
2025-08-01 14:39:20,687 - DEBUG - 批次 107 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:20,688 - DEBUG - 批次 107 数据类型: torch.float32
2025-08-01 14:39:20,688 - DEBUG - 批次 107 设备: cuda:0
2025-08-01 14:39:20,688 - DEBUG - 批次 107 数据范围: [0.000, 1.000]
2025-08-01 14:39:20,689 - ERROR - 批次 107 处理失败，已重试 3 次
2025-08-01 14:39:20,689 - ERROR - 错误类型: ValueError
2025-08-01 14:39:20,690 - ERROR - 错误信息: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:20,690 - ERROR - 详细堆栈跟踪:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:39:20,690 - ERROR - 失败批次信息:
2025-08-01 14:39:20,690 - ERROR -   - 批次索引: 107
2025-08-01 14:39:20,690 - ERROR -   - 批次大小: 2
2025-08-01 14:39:20,690 - ERROR -   - 无法获取批次详细信息
2025-08-01 14:39:20,691 - DEBUG - 批次 108 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:20,691 - DEBUG - 批次 108 数据类型: torch.float32
2025-08-01 14:39:20,691 - DEBUG - 批次 108 设备: cuda:0
2025-08-01 14:39:20,692 - DEBUG - 批次 108 数据范围: [0.000, 1.000]
2025-08-01 14:39:20,692 - WARNING - 批次 108 处理失败，重试 1/3
2025-08-01 14:39:20,692 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:20,692 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:39:20,692 - DEBUG - 已清理GPU缓存
2025-08-01 14:39:20,723 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:39:20,748 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:39:21,193 - DEBUG - 批次 108 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:21,194 - DEBUG - 批次 108 数据类型: torch.float32
2025-08-01 14:39:21,194 - DEBUG - 批次 108 设备: cuda:0
2025-08-01 14:39:21,194 - DEBUG - 批次 108 数据范围: [0.000, 1.000]
2025-08-01 14:39:21,195 - WARNING - 批次 108 处理失败，重试 2/3
2025-08-01 14:39:21,195 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:21,195 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:39:21,195 - DEBUG - 已清理GPU缓存
2025-08-01 14:39:21,697 - DEBUG - 批次 108 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:21,697 - DEBUG - 批次 108 数据类型: torch.float32
2025-08-01 14:39:21,697 - DEBUG - 批次 108 设备: cuda:0
2025-08-01 14:39:21,698 - DEBUG - 批次 108 数据范围: [0.000, 1.000]
2025-08-01 14:39:21,698 - ERROR - 批次 108 处理失败，已重试 3 次
2025-08-01 14:39:21,699 - ERROR - 错误类型: ValueError
2025-08-01 14:39:21,699 - ERROR - 错误信息: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:21,699 - ERROR - 详细堆栈跟踪:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:39:21,699 - ERROR - 失败批次信息:
2025-08-01 14:39:21,699 - ERROR -   - 批次索引: 108
2025-08-01 14:39:21,699 - ERROR -   - 批次大小: 2
2025-08-01 14:39:21,699 - ERROR -   - 无法获取批次详细信息
2025-08-01 14:39:21,700 - DEBUG - 批次 109 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:21,700 - DEBUG - 批次 109 数据类型: torch.float32
2025-08-01 14:39:21,700 - DEBUG - 批次 109 设备: cuda:0
2025-08-01 14:39:21,700 - DEBUG - 批次 109 数据范围: [0.000, 0.992]
2025-08-01 14:39:21,701 - WARNING - 批次 109 处理失败，重试 1/3
2025-08-01 14:39:21,701 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:21,701 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:39:21,701 - DEBUG - 已清理GPU缓存
2025-08-01 14:39:21,733 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:39:21,759 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:39:22,202 - DEBUG - 批次 109 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:22,203 - DEBUG - 批次 109 数据类型: torch.float32
2025-08-01 14:39:22,203 - DEBUG - 批次 109 设备: cuda:0
2025-08-01 14:39:22,203 - DEBUG - 批次 109 数据范围: [0.000, 0.992]
2025-08-01 14:39:22,204 - WARNING - 批次 109 处理失败，重试 2/3
2025-08-01 14:39:22,204 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:22,204 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:39:22,204 - DEBUG - 已清理GPU缓存
2025-08-01 14:39:22,706 - DEBUG - 批次 109 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:22,706 - DEBUG - 批次 109 数据类型: torch.float32
2025-08-01 14:39:22,706 - DEBUG - 批次 109 设备: cuda:0
2025-08-01 14:39:22,707 - DEBUG - 批次 109 数据范围: [0.000, 0.992]
2025-08-01 14:39:22,707 - ERROR - 批次 109 处理失败，已重试 3 次
2025-08-01 14:39:22,708 - ERROR - 错误类型: ValueError
2025-08-01 14:39:22,708 - ERROR - 错误信息: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:22,708 - ERROR - 详细堆栈跟踪:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:39:22,708 - ERROR - 失败批次信息:
2025-08-01 14:39:22,708 - ERROR -   - 批次索引: 109
2025-08-01 14:39:22,708 - ERROR -   - 批次大小: 2
2025-08-01 14:39:22,709 - ERROR -   - 无法获取批次详细信息
2025-08-01 14:39:22,710 - DEBUG - 批次 110 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:22,710 - DEBUG - 批次 110 数据类型: torch.float32
2025-08-01 14:39:22,710 - DEBUG - 批次 110 设备: cuda:0
2025-08-01 14:39:22,710 - DEBUG - 批次 110 数据范围: [0.000, 0.992]
2025-08-01 14:39:22,710 - WARNING - 批次 110 处理失败，重试 1/3
2025-08-01 14:39:22,710 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:22,710 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:39:22,710 - DEBUG - 已清理GPU缓存
2025-08-01 14:39:22,741 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:39:22,766 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:39:23,212 - DEBUG - 批次 110 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:23,212 - DEBUG - 批次 110 数据类型: torch.float32
2025-08-01 14:39:23,212 - DEBUG - 批次 110 设备: cuda:0
2025-08-01 14:39:23,213 - DEBUG - 批次 110 数据范围: [0.000, 0.992]
2025-08-01 14:39:23,213 - WARNING - 批次 110 处理失败，重试 2/3
2025-08-01 14:39:23,213 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:23,213 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:39:23,214 - DEBUG - 已清理GPU缓存
2025-08-01 14:39:23,715 - DEBUG - 批次 110 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:23,715 - DEBUG - 批次 110 数据类型: torch.float32
2025-08-01 14:39:23,715 - DEBUG - 批次 110 设备: cuda:0
2025-08-01 14:39:23,716 - DEBUG - 批次 110 数据范围: [0.000, 0.992]
2025-08-01 14:39:23,717 - ERROR - 批次 110 处理失败，已重试 3 次
2025-08-01 14:39:23,717 - ERROR - 错误类型: ValueError
2025-08-01 14:39:23,717 - ERROR - 错误信息: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:23,717 - ERROR - 详细堆栈跟踪:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:39:23,717 - ERROR - 失败批次信息:
2025-08-01 14:39:23,717 - ERROR -   - 批次索引: 110
2025-08-01 14:39:23,717 - ERROR -   - 批次大小: 2
2025-08-01 14:39:23,718 - ERROR -   - 无法获取批次详细信息
2025-08-01 14:39:23,718 - DEBUG - 批次 111 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:23,719 - DEBUG - 批次 111 数据类型: torch.float32
2025-08-01 14:39:23,719 - DEBUG - 批次 111 设备: cuda:0
2025-08-01 14:39:23,719 - DEBUG - 批次 111 数据范围: [0.000, 1.000]
2025-08-01 14:39:23,719 - WARNING - 批次 111 处理失败，重试 1/3
2025-08-01 14:39:23,719 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:23,719 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:39:23,719 - DEBUG - 已清理GPU缓存
2025-08-01 14:39:23,752 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:39:23,777 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:39:24,220 - DEBUG - 批次 111 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:24,221 - DEBUG - 批次 111 数据类型: torch.float32
2025-08-01 14:39:24,221 - DEBUG - 批次 111 设备: cuda:0
2025-08-01 14:39:24,222 - DEBUG - 批次 111 数据范围: [0.000, 1.000]
2025-08-01 14:39:24,222 - WARNING - 批次 111 处理失败，重试 2/3
2025-08-01 14:39:24,222 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:24,222 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:39:24,222 - DEBUG - 已清理GPU缓存
2025-08-01 14:39:24,724 - DEBUG - 批次 111 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:24,724 - DEBUG - 批次 111 数据类型: torch.float32
2025-08-01 14:39:24,724 - DEBUG - 批次 111 设备: cuda:0
2025-08-01 14:39:24,725 - DEBUG - 批次 111 数据范围: [0.000, 1.000]
2025-08-01 14:39:24,726 - ERROR - 批次 111 处理失败，已重试 3 次
2025-08-01 14:39:24,726 - ERROR - 错误类型: ValueError
2025-08-01 14:39:24,726 - ERROR - 错误信息: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:24,726 - ERROR - 详细堆栈跟踪:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:39:24,726 - ERROR - 失败批次信息:
2025-08-01 14:39:24,726 - ERROR -   - 批次索引: 111
2025-08-01 14:39:24,726 - ERROR -   - 批次大小: 2
2025-08-01 14:39:24,726 - ERROR -   - 无法获取批次详细信息
2025-08-01 14:39:24,727 - DEBUG - 批次 112 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:24,727 - DEBUG - 批次 112 数据类型: torch.float32
2025-08-01 14:39:24,727 - DEBUG - 批次 112 设备: cuda:0
2025-08-01 14:39:24,728 - DEBUG - 批次 112 数据范围: [0.000, 1.000]
2025-08-01 14:39:24,728 - WARNING - 批次 112 处理失败，重试 1/3
2025-08-01 14:39:24,728 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:24,728 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:39:24,728 - DEBUG - 已清理GPU缓存
2025-08-01 14:39:24,760 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:39:24,785 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:39:25,229 - DEBUG - 批次 112 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:25,230 - DEBUG - 批次 112 数据类型: torch.float32
2025-08-01 14:39:25,230 - DEBUG - 批次 112 设备: cuda:0
2025-08-01 14:39:25,231 - DEBUG - 批次 112 数据范围: [0.000, 1.000]
2025-08-01 14:39:25,231 - WARNING - 批次 112 处理失败，重试 2/3
2025-08-01 14:39:25,231 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:25,231 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:39:25,231 - DEBUG - 已清理GPU缓存
2025-08-01 14:39:25,733 - DEBUG - 批次 112 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:25,733 - DEBUG - 批次 112 数据类型: torch.float32
2025-08-01 14:39:25,733 - DEBUG - 批次 112 设备: cuda:0
2025-08-01 14:39:25,734 - DEBUG - 批次 112 数据范围: [0.000, 1.000]
2025-08-01 14:39:25,735 - ERROR - 批次 112 处理失败，已重试 3 次
2025-08-01 14:39:25,735 - ERROR - 错误类型: ValueError
2025-08-01 14:39:25,735 - ERROR - 错误信息: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:25,735 - ERROR - 详细堆栈跟踪:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:39:25,735 - ERROR - 失败批次信息:
2025-08-01 14:39:25,735 - ERROR -   - 批次索引: 112
2025-08-01 14:39:25,735 - ERROR -   - 批次大小: 2
2025-08-01 14:39:25,736 - ERROR -   - 无法获取批次详细信息
2025-08-01 14:39:25,737 - DEBUG - 批次 113 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:25,737 - DEBUG - 批次 113 数据类型: torch.float32
2025-08-01 14:39:25,737 - DEBUG - 批次 113 设备: cuda:0
2025-08-01 14:39:25,737 - DEBUG - 批次 113 数据范围: [0.000, 0.996]
2025-08-01 14:39:25,737 - WARNING - 批次 113 处理失败，重试 1/3
2025-08-01 14:39:25,737 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:25,737 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:39:25,737 - DEBUG - 已清理GPU缓存
2025-08-01 14:39:25,769 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:39:25,794 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:39:26,239 - DEBUG - 批次 113 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:26,239 - DEBUG - 批次 113 数据类型: torch.float32
2025-08-01 14:39:26,239 - DEBUG - 批次 113 设备: cuda:0
2025-08-01 14:39:26,240 - DEBUG - 批次 113 数据范围: [0.000, 0.996]
2025-08-01 14:39:26,241 - WARNING - 批次 113 处理失败，重试 2/3
2025-08-01 14:39:26,241 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:26,241 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:39:26,241 - DEBUG - 已清理GPU缓存
2025-08-01 14:39:26,742 - DEBUG - 批次 113 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:26,743 - DEBUG - 批次 113 数据类型: torch.float32
2025-08-01 14:39:26,743 - DEBUG - 批次 113 设备: cuda:0
2025-08-01 14:39:26,743 - DEBUG - 批次 113 数据范围: [0.000, 0.996]
2025-08-01 14:39:26,744 - ERROR - 批次 113 处理失败，已重试 3 次
2025-08-01 14:39:26,744 - ERROR - 错误类型: ValueError
2025-08-01 14:39:26,745 - ERROR - 错误信息: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:26,745 - ERROR - 详细堆栈跟踪:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:39:26,745 - ERROR - 失败批次信息:
2025-08-01 14:39:26,745 - ERROR -   - 批次索引: 113
2025-08-01 14:39:26,745 - ERROR -   - 批次大小: 2
2025-08-01 14:39:26,745 - ERROR -   - 无法获取批次详细信息
2025-08-01 14:39:26,746 - DEBUG - 批次 114 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:26,746 - DEBUG - 批次 114 数据类型: torch.float32
2025-08-01 14:39:26,746 - DEBUG - 批次 114 设备: cuda:0
2025-08-01 14:39:26,746 - DEBUG - 批次 114 数据范围: [0.000, 1.000]
2025-08-01 14:39:26,747 - WARNING - 批次 114 处理失败，重试 1/3
2025-08-01 14:39:26,747 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:26,747 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:39:26,747 - DEBUG - 已清理GPU缓存
2025-08-01 14:39:26,777 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:39:26,803 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:39:27,248 - DEBUG - 批次 114 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:27,249 - DEBUG - 批次 114 数据类型: torch.float32
2025-08-01 14:39:27,249 - DEBUG - 批次 114 设备: cuda:0
2025-08-01 14:39:27,249 - DEBUG - 批次 114 数据范围: [0.000, 1.000]
2025-08-01 14:39:27,250 - WARNING - 批次 114 处理失败，重试 2/3
2025-08-01 14:39:27,250 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:27,250 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:39:27,250 - DEBUG - 已清理GPU缓存
2025-08-01 14:39:27,751 - DEBUG - 批次 114 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:27,752 - DEBUG - 批次 114 数据类型: torch.float32
2025-08-01 14:39:27,752 - DEBUG - 批次 114 设备: cuda:0
2025-08-01 14:39:27,752 - DEBUG - 批次 114 数据范围: [0.000, 1.000]
2025-08-01 14:39:27,753 - ERROR - 批次 114 处理失败，已重试 3 次
2025-08-01 14:39:27,753 - ERROR - 错误类型: ValueError
2025-08-01 14:39:27,753 - ERROR - 错误信息: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:27,754 - ERROR - 详细堆栈跟踪:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:39:27,754 - ERROR - 失败批次信息:
2025-08-01 14:39:27,754 - ERROR -   - 批次索引: 114
2025-08-01 14:39:27,754 - ERROR -   - 批次大小: 2
2025-08-01 14:39:27,754 - ERROR -   - 无法获取批次详细信息
2025-08-01 14:39:27,755 - DEBUG - 批次 115 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:27,755 - DEBUG - 批次 115 数据类型: torch.float32
2025-08-01 14:39:27,755 - DEBUG - 批次 115 设备: cuda:0
2025-08-01 14:39:27,755 - DEBUG - 批次 115 数据范围: [0.000, 1.000]
2025-08-01 14:39:27,755 - WARNING - 批次 115 处理失败，重试 1/3
2025-08-01 14:39:27,756 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:27,756 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:39:27,756 - DEBUG - 已清理GPU缓存
2025-08-01 14:39:27,787 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:39:27,814 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:39:28,257 - DEBUG - 批次 115 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:28,257 - DEBUG - 批次 115 数据类型: torch.float32
2025-08-01 14:39:28,258 - DEBUG - 批次 115 设备: cuda:0
2025-08-01 14:39:28,258 - DEBUG - 批次 115 数据范围: [0.000, 1.000]
2025-08-01 14:39:28,259 - WARNING - 批次 115 处理失败，重试 2/3
2025-08-01 14:39:28,259 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:28,259 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:39:28,259 - DEBUG - 已清理GPU缓存
2025-08-01 14:39:28,760 - DEBUG - 批次 115 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:28,761 - DEBUG - 批次 115 数据类型: torch.float32
2025-08-01 14:39:28,761 - DEBUG - 批次 115 设备: cuda:0
2025-08-01 14:39:28,761 - DEBUG - 批次 115 数据范围: [0.000, 1.000]
2025-08-01 14:39:28,761 - ERROR - 批次 115 处理失败，已重试 3 次
2025-08-01 14:39:28,762 - ERROR - 错误类型: ValueError
2025-08-01 14:39:28,762 - ERROR - 错误信息: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:28,762 - ERROR - 详细堆栈跟踪:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:39:28,762 - ERROR - 失败批次信息:
2025-08-01 14:39:28,762 - ERROR -   - 批次索引: 115
2025-08-01 14:39:28,762 - ERROR -   - 批次大小: 2
2025-08-01 14:39:28,762 - ERROR -   - 无法获取批次详细信息
2025-08-01 14:39:28,763 - DEBUG - 批次 116 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:28,763 - DEBUG - 批次 116 数据类型: torch.float32
2025-08-01 14:39:28,763 - DEBUG - 批次 116 设备: cuda:0
2025-08-01 14:39:28,763 - DEBUG - 批次 116 数据范围: [0.000, 1.000]
2025-08-01 14:39:28,763 - WARNING - 批次 116 处理失败，重试 1/3
2025-08-01 14:39:28,763 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:28,763 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:39:28,764 - DEBUG - 已清理GPU缓存
2025-08-01 14:39:28,794 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:39:28,820 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:39:29,265 - DEBUG - 批次 116 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:29,265 - DEBUG - 批次 116 数据类型: torch.float32
2025-08-01 14:39:29,265 - DEBUG - 批次 116 设备: cuda:0
2025-08-01 14:39:29,266 - DEBUG - 批次 116 数据范围: [0.000, 1.000]
2025-08-01 14:39:29,267 - WARNING - 批次 116 处理失败，重试 2/3
2025-08-01 14:39:29,267 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:29,267 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:39:29,267 - DEBUG - 已清理GPU缓存
2025-08-01 14:39:29,768 - DEBUG - 批次 116 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:29,769 - DEBUG - 批次 116 数据类型: torch.float32
2025-08-01 14:39:29,769 - DEBUG - 批次 116 设备: cuda:0
2025-08-01 14:39:29,769 - DEBUG - 批次 116 数据范围: [0.000, 1.000]
2025-08-01 14:39:29,770 - ERROR - 批次 116 处理失败，已重试 3 次
2025-08-01 14:39:29,770 - ERROR - 错误类型: ValueError
2025-08-01 14:39:29,770 - ERROR - 错误信息: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:29,771 - ERROR - 详细堆栈跟踪:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:39:29,771 - ERROR - 失败批次信息:
2025-08-01 14:39:29,771 - ERROR -   - 批次索引: 116
2025-08-01 14:39:29,771 - ERROR -   - 批次大小: 2
2025-08-01 14:39:29,771 - ERROR -   - 无法获取批次详细信息
2025-08-01 14:39:29,772 - DEBUG - 批次 117 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:29,772 - DEBUG - 批次 117 数据类型: torch.float32
2025-08-01 14:39:29,772 - DEBUG - 批次 117 设备: cuda:0
2025-08-01 14:39:29,772 - DEBUG - 批次 117 数据范围: [0.000, 0.988]
2025-08-01 14:39:29,772 - WARNING - 批次 117 处理失败，重试 1/3
2025-08-01 14:39:29,772 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:29,772 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:39:29,772 - DEBUG - 已清理GPU缓存
2025-08-01 14:39:29,805 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:39:29,836 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:39:30,274 - DEBUG - 批次 117 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:30,274 - DEBUG - 批次 117 数据类型: torch.float32
2025-08-01 14:39:30,274 - DEBUG - 批次 117 设备: cuda:0
2025-08-01 14:39:30,275 - DEBUG - 批次 117 数据范围: [0.000, 0.988]
2025-08-01 14:39:30,275 - WARNING - 批次 117 处理失败，重试 2/3
2025-08-01 14:39:30,276 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:30,276 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:39:30,276 - DEBUG - 已清理GPU缓存
2025-08-01 14:39:30,777 - DEBUG - 批次 117 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:30,777 - DEBUG - 批次 117 数据类型: torch.float32
2025-08-01 14:39:30,778 - DEBUG - 批次 117 设备: cuda:0
2025-08-01 14:39:30,778 - DEBUG - 批次 117 数据范围: [0.000, 0.988]
2025-08-01 14:39:30,779 - ERROR - 批次 117 处理失败，已重试 3 次
2025-08-01 14:39:30,779 - ERROR - 错误类型: ValueError
2025-08-01 14:39:30,779 - ERROR - 错误信息: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:30,779 - ERROR - 详细堆栈跟踪:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:39:30,780 - ERROR - 失败批次信息:
2025-08-01 14:39:30,780 - ERROR -   - 批次索引: 117
2025-08-01 14:39:30,780 - ERROR -   - 批次大小: 2
2025-08-01 14:39:30,780 - ERROR -   - 无法获取批次详细信息
2025-08-01 14:39:30,781 - DEBUG - 批次 118 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:30,781 - DEBUG - 批次 118 数据类型: torch.float32
2025-08-01 14:39:30,781 - DEBUG - 批次 118 设备: cuda:0
2025-08-01 14:39:30,781 - DEBUG - 批次 118 数据范围: [0.000, 0.992]
2025-08-01 14:39:30,781 - WARNING - 批次 118 处理失败，重试 1/3
2025-08-01 14:39:30,781 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:30,781 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:39:30,781 - DEBUG - 已清理GPU缓存
2025-08-01 14:39:30,813 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:39:30,840 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:39:31,283 - DEBUG - 批次 118 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:31,283 - DEBUG - 批次 118 数据类型: torch.float32
2025-08-01 14:39:31,283 - DEBUG - 批次 118 设备: cuda:0
2025-08-01 14:39:31,284 - DEBUG - 批次 118 数据范围: [0.000, 0.992]
2025-08-01 14:39:31,284 - WARNING - 批次 118 处理失败，重试 2/3
2025-08-01 14:39:31,285 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:31,285 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:39:31,285 - DEBUG - 已清理GPU缓存
2025-08-01 14:39:31,786 - DEBUG - 批次 118 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:31,787 - DEBUG - 批次 118 数据类型: torch.float32
2025-08-01 14:39:31,787 - DEBUG - 批次 118 设备: cuda:0
2025-08-01 14:39:31,787 - DEBUG - 批次 118 数据范围: [0.000, 0.992]
2025-08-01 14:39:31,788 - ERROR - 批次 118 处理失败，已重试 3 次
2025-08-01 14:39:31,788 - ERROR - 错误类型: ValueError
2025-08-01 14:39:31,788 - ERROR - 错误信息: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:31,789 - ERROR - 详细堆栈跟踪:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:39:31,789 - ERROR - 失败批次信息:
2025-08-01 14:39:31,789 - ERROR -   - 批次索引: 118
2025-08-01 14:39:31,789 - ERROR -   - 批次大小: 2
2025-08-01 14:39:31,789 - ERROR -   - 无法获取批次详细信息
2025-08-01 14:39:31,790 - DEBUG - 批次 119 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:31,790 - DEBUG - 批次 119 数据类型: torch.float32
2025-08-01 14:39:31,790 - DEBUG - 批次 119 设备: cuda:0
2025-08-01 14:39:31,790 - DEBUG - 批次 119 数据范围: [0.000, 0.992]
2025-08-01 14:39:31,790 - WARNING - 批次 119 处理失败，重试 1/3
2025-08-01 14:39:31,790 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:31,790 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:39:31,791 - DEBUG - 已清理GPU缓存
2025-08-01 14:39:31,822 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:39:31,848 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:39:32,292 - DEBUG - 批次 119 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:32,292 - DEBUG - 批次 119 数据类型: torch.float32
2025-08-01 14:39:32,292 - DEBUG - 批次 119 设备: cuda:0
2025-08-01 14:39:32,293 - DEBUG - 批次 119 数据范围: [0.000, 0.992]
2025-08-01 14:39:32,293 - WARNING - 批次 119 处理失败，重试 2/3
2025-08-01 14:39:32,293 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:32,294 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:39:32,294 - DEBUG - 已清理GPU缓存
2025-08-01 14:39:32,795 - DEBUG - 批次 119 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:32,795 - DEBUG - 批次 119 数据类型: torch.float32
2025-08-01 14:39:32,796 - DEBUG - 批次 119 设备: cuda:0
2025-08-01 14:39:32,796 - DEBUG - 批次 119 数据范围: [0.000, 0.992]
2025-08-01 14:39:32,797 - ERROR - 批次 119 处理失败，已重试 3 次
2025-08-01 14:39:32,797 - ERROR - 错误类型: ValueError
2025-08-01 14:39:32,797 - ERROR - 错误信息: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:32,797 - ERROR - 详细堆栈跟踪:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:39:32,797 - ERROR - 失败批次信息:
2025-08-01 14:39:32,798 - ERROR -   - 批次索引: 119
2025-08-01 14:39:32,798 - ERROR -   - 批次大小: 2
2025-08-01 14:39:32,798 - ERROR -   - 无法获取批次详细信息
2025-08-01 14:39:32,799 - DEBUG - 批次 120 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:32,799 - DEBUG - 批次 120 数据类型: torch.float32
2025-08-01 14:39:32,799 - DEBUG - 批次 120 设备: cuda:0
2025-08-01 14:39:32,799 - DEBUG - 批次 120 数据范围: [0.000, 0.996]
2025-08-01 14:39:32,799 - WARNING - 批次 120 处理失败，重试 1/3
2025-08-01 14:39:32,799 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:32,799 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:39:32,800 - DEBUG - 已清理GPU缓存
2025-08-01 14:39:32,834 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:39:32,860 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:39:33,301 - DEBUG - 批次 120 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:33,301 - DEBUG - 批次 120 数据类型: torch.float32
2025-08-01 14:39:33,301 - DEBUG - 批次 120 设备: cuda:0
2025-08-01 14:39:33,301 - DEBUG - 批次 120 数据范围: [0.000, 0.996]
2025-08-01 14:39:33,302 - WARNING - 批次 120 处理失败，重试 2/3
2025-08-01 14:39:33,302 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:33,302 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:39:33,302 - DEBUG - 已清理GPU缓存
2025-08-01 14:39:33,803 - DEBUG - 批次 120 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:33,804 - DEBUG - 批次 120 数据类型: torch.float32
2025-08-01 14:39:33,804 - DEBUG - 批次 120 设备: cuda:0
2025-08-01 14:39:33,804 - DEBUG - 批次 120 数据范围: [0.000, 0.996]
2025-08-01 14:39:33,805 - ERROR - 批次 120 处理失败，已重试 3 次
2025-08-01 14:39:33,805 - ERROR - 错误类型: ValueError
2025-08-01 14:39:33,805 - ERROR - 错误信息: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:33,806 - ERROR - 详细堆栈跟踪:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:39:33,806 - ERROR - 失败批次信息:
2025-08-01 14:39:33,806 - ERROR -   - 批次索引: 120
2025-08-01 14:39:33,806 - ERROR -   - 批次大小: 2
2025-08-01 14:39:33,806 - ERROR -   - 无法获取批次详细信息
2025-08-01 14:39:33,807 - DEBUG - 批次 121 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:33,807 - DEBUG - 批次 121 数据类型: torch.float32
2025-08-01 14:39:33,807 - DEBUG - 批次 121 设备: cuda:0
2025-08-01 14:39:33,807 - DEBUG - 批次 121 数据范围: [0.000, 0.996]
2025-08-01 14:39:33,808 - WARNING - 批次 121 处理失败，重试 1/3
2025-08-01 14:39:33,808 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:33,808 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:39:33,808 - DEBUG - 已清理GPU缓存
2025-08-01 14:39:33,840 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:39:33,865 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:39:34,309 - DEBUG - 批次 121 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:34,310 - DEBUG - 批次 121 数据类型: torch.float32
2025-08-01 14:39:34,310 - DEBUG - 批次 121 设备: cuda:0
2025-08-01 14:39:34,310 - DEBUG - 批次 121 数据范围: [0.000, 0.996]
2025-08-01 14:39:34,311 - WARNING - 批次 121 处理失败，重试 2/3
2025-08-01 14:39:34,311 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:34,311 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:39:34,312 - DEBUG - 已清理GPU缓存
2025-08-01 14:39:34,813 - DEBUG - 批次 121 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:34,813 - DEBUG - 批次 121 数据类型: torch.float32
2025-08-01 14:39:34,813 - DEBUG - 批次 121 设备: cuda:0
2025-08-01 14:39:34,814 - DEBUG - 批次 121 数据范围: [0.000, 0.996]
2025-08-01 14:39:34,815 - ERROR - 批次 121 处理失败，已重试 3 次
2025-08-01 14:39:34,815 - ERROR - 错误类型: ValueError
2025-08-01 14:39:34,815 - ERROR - 错误信息: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:34,815 - ERROR - 详细堆栈跟踪:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:39:34,815 - ERROR - 失败批次信息:
2025-08-01 14:39:34,815 - ERROR -   - 批次索引: 121
2025-08-01 14:39:34,815 - ERROR -   - 批次大小: 2
2025-08-01 14:39:34,816 - ERROR -   - 无法获取批次详细信息
2025-08-01 14:39:34,817 - DEBUG - 批次 122 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:34,817 - DEBUG - 批次 122 数据类型: torch.float32
2025-08-01 14:39:34,817 - DEBUG - 批次 122 设备: cuda:0
2025-08-01 14:39:34,817 - DEBUG - 批次 122 数据范围: [0.000, 1.000]
2025-08-01 14:39:34,817 - WARNING - 批次 122 处理失败，重试 1/3
2025-08-01 14:39:34,817 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:34,817 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:39:34,817 - DEBUG - 已清理GPU缓存
2025-08-01 14:39:34,849 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:39:34,877 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:39:35,319 - DEBUG - 批次 122 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:35,319 - DEBUG - 批次 122 数据类型: torch.float32
2025-08-01 14:39:35,319 - DEBUG - 批次 122 设备: cuda:0
2025-08-01 14:39:35,320 - DEBUG - 批次 122 数据范围: [0.000, 1.000]
2025-08-01 14:39:35,320 - WARNING - 批次 122 处理失败，重试 2/3
2025-08-01 14:39:35,321 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:35,321 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:39:35,321 - DEBUG - 已清理GPU缓存
2025-08-01 14:39:35,822 - DEBUG - 批次 122 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:35,823 - DEBUG - 批次 122 数据类型: torch.float32
2025-08-01 14:39:35,823 - DEBUG - 批次 122 设备: cuda:0
2025-08-01 14:39:35,823 - DEBUG - 批次 122 数据范围: [0.000, 1.000]
2025-08-01 14:39:35,824 - ERROR - 批次 122 处理失败，已重试 3 次
2025-08-01 14:39:35,824 - ERROR - 错误类型: ValueError
2025-08-01 14:39:35,824 - ERROR - 错误信息: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:35,825 - ERROR - 详细堆栈跟踪:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:39:35,825 - ERROR - 失败批次信息:
2025-08-01 14:39:35,825 - ERROR -   - 批次索引: 122
2025-08-01 14:39:35,825 - ERROR -   - 批次大小: 2
2025-08-01 14:39:35,825 - ERROR -   - 无法获取批次详细信息
2025-08-01 14:39:35,826 - DEBUG - 批次 123 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:35,826 - DEBUG - 批次 123 数据类型: torch.float32
2025-08-01 14:39:35,826 - DEBUG - 批次 123 设备: cuda:0
2025-08-01 14:39:35,826 - DEBUG - 批次 123 数据范围: [0.000, 1.000]
2025-08-01 14:39:35,826 - WARNING - 批次 123 处理失败，重试 1/3
2025-08-01 14:39:35,827 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:35,827 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:39:35,827 - DEBUG - 已清理GPU缓存
2025-08-01 14:39:35,859 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:39:35,885 - WARNING - 帧对形状不匹配: 期望 (3, 2, 256, 256), 实际 torch.Size([3, 2, 256, 411])
2025-08-01 14:39:36,328 - DEBUG - 批次 123 数据形状: torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:36,328 - DEBUG - 批次 123 数据类型: torch.float32
2025-08-01 14:39:36,329 - DEBUG - 批次 123 设备: cuda:0
2025-08-01 14:39:36,329 - DEBUG - 批次 123 数据范围: [0.000, 1.000]
2025-08-01 14:39:36,330 - WARNING - 批次 123 处理失败，重试 2/3
2025-08-01 14:39:36,330 - WARNING - 错误: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])
2025-08-01 14:39:36,330 - DEBUG - 详细错误:
Traceback (most recent call last):
  File "/home/<USER>/johnny_ws/lapa_ws/scripts/inference/laq_inference.py", line 314, in run_inference
    raise ValueError(f"数据形状不匹配: 期望 {expected_shape}, 实际 {frame_pairs.shape}")
ValueError: 数据形状不匹配: 期望 (2, 3, 2, 256, 256), 实际 torch.Size([2, 3, 2, 256, 411])

2025-08-01 14:39:36,330 - DEBUG - 已清理GPU缓存
2025-08-01 14:39:36,630 - INFO - 用户中断推理过程
2025-08-01 14:41:40,731 - INFO - 正在加载LAQ模型...
2025-08-01 14:41:43,353 - INFO - 从 models/laq_openx.pt 加载模型权重...
2025-08-01 14:41:44,238 - INFO - ✓ 模型验证通过，测试输出形状: torch.Size([1, 4])
2025-08-01 14:41:44,238 - INFO - ✓ LAQ模型加载成功，使用设备: cuda
2025-08-01 14:41:44,238 - INFO -   - 模型参数: dim=1024, codebook_size=8
2025-08-01 14:41:44,239 - INFO - ✓ 图像变换设置完成，目标尺寸: 256x256
2025-08-01 14:41:44,239 - INFO - 开始推理任务...
2025-08-01 14:41:44,239 - INFO - 输入文件: test_single_batch/single_input.jsonl
2025-08-01 14:41:44,239 - INFO - 输出文件: test_single_batch/single_output.jsonl
2025-08-01 14:41:44,239 - INFO - 加载了 1 个帧对数据
2025-08-01 14:41:44,348 - DEBUG - 批次 0 数据形状: torch.Size([1, 3, 2, 256, 256])
2025-08-01 14:41:44,348 - DEBUG - 批次 0 数据类型: torch.float32
2025-08-01 14:41:44,349 - DEBUG - 批次 0 设备: cuda:0
2025-08-01 14:41:44,369 - DEBUG - 批次 0 数据范围: [0.020, 1.000]
2025-08-01 14:41:44,386 - DEBUG - 模型输入形状: torch.Size([1, 3, 2, 256, 256])
2025-08-01 14:41:44,386 - DEBUG - 开始执行模型推理...
2025-08-01 14:41:44,412 - DEBUG - 模型推理完成，结果形状: torch.Size([1, 4])
2025-08-01 14:41:44,438 - INFO - 批次处理完成: 总批次 1，失败批次 0，成功样本 1
2025-08-01 14:41:44,438 - INFO - 保存推理结果到 test_single_batch/single_output.jsonl...
2025-08-01 14:41:44,439 - INFO - ✓ 推理完成!
2025-08-01 14:41:44,439 - INFO -   - 处理样本数: 1
2025-08-01 14:41:44,439 - INFO -   - 总耗时: 0.20秒
2025-08-01 14:41:44,439 - INFO -   - 平均速度: 5.02样本/秒
2025-08-01 14:41:44,439 - INFO -   - 结果保存至: test_single_batch/single_output.jsonl
2025-08-01 14:41:47,982 - INFO - 正在加载LAQ模型...
2025-08-01 14:41:50,668 - INFO - 从 models/laq_openx.pt 加载模型权重...
2025-08-01 14:41:51,668 - INFO - ✓ 模型验证通过，测试输出形状: torch.Size([1, 4])
2025-08-01 14:41:51,668 - INFO - ✓ LAQ模型加载成功，使用设备: cuda
2025-08-01 14:41:51,668 - INFO -   - 模型参数: dim=1024, codebook_size=8
2025-08-01 14:41:51,669 - INFO - ✓ 图像变换设置完成，目标尺寸: 256x256
2025-08-01 14:41:51,669 - INFO - 开始推理任务...
2025-08-01 14:41:51,669 - INFO - 输入文件: test_mixed_size_data/mixed_size_input.jsonl
2025-08-01 14:41:51,669 - INFO - 输出文件: test_mixed_size_data/mixed_size_output.jsonl
2025-08-01 14:41:51,669 - INFO - 加载了 4 个帧对数据
2025-08-01 14:41:51,890 - INFO - 批次处理完成: 总批次 2，失败批次 0，成功样本 4
2025-08-01 14:41:51,890 - INFO - 保存推理结果到 test_mixed_size_data/mixed_size_output.jsonl...
2025-08-01 14:41:51,891 - INFO - ✓ 推理完成!
2025-08-01 14:41:51,891 - INFO -   - 处理样本数: 4
2025-08-01 14:41:51,891 - INFO -   - 总耗时: 0.22秒
2025-08-01 14:41:51,891 - INFO -   - 平均速度: 18.05样本/秒
2025-08-01 14:41:51,891 - INFO -   - 结果保存至: test_mixed_size_data/mixed_size_output.jsonl
2025-08-01 14:42:24,217 - INFO - 正在加载LAQ模型...
2025-08-01 14:42:26,911 - INFO - 从 models/laq_openx.pt 加载模型权重...
2025-08-01 14:42:27,787 - INFO - ✓ 模型验证通过，测试输出形状: torch.Size([1, 4])
2025-08-01 14:42:27,788 - INFO - ✓ LAQ模型加载成功，使用设备: cuda
2025-08-01 14:42:27,788 - INFO -   - 模型参数: dim=1024, codebook_size=8
2025-08-01 14:42:27,788 - INFO - ✓ 图像变换设置完成，目标尺寸: 256x256
2025-08-01 14:42:27,788 - INFO - 开始推理任务...
2025-08-01 14:42:27,788 - INFO - 输入文件: data/smart_preprocessed_fixed/laq_dataset_hybrid.jsonl
2025-08-01 14:42:27,788 - INFO - 输出文件: results/fixed_laq_results.jsonl
2025-08-01 14:42:27,796 - INFO - 加载了 1644 个帧对数据
2025-08-01 14:42:30,890 - INFO - 已处理 100/411 批次，成功样本: 400，失败批次: 0
2025-08-01 14:42:33,718 - INFO - 已处理 200/411 批次，成功样本: 800，失败批次: 0
2025-08-01 14:42:36,474 - INFO - 已处理 300/411 批次，成功样本: 1200，失败批次: 0
2025-08-01 14:42:39,161 - INFO - 已处理 400/411 批次，成功样本: 1600，失败批次: 0
2025-08-01 14:42:39,489 - INFO - 批次处理完成: 总批次 411，失败批次 0，成功样本 1644
2025-08-01 14:42:39,490 - INFO - 保存推理结果到 results/fixed_laq_results.jsonl...
2025-08-01 14:42:39,501 - INFO - ✓ 推理完成!
2025-08-01 14:42:39,502 - INFO -   - 处理样本数: 1644
2025-08-01 14:42:39,502 - INFO -   - 总耗时: 11.70秒
2025-08-01 14:42:39,502 - INFO -   - 平均速度: 140.45样本/秒
2025-08-01 14:42:39,502 - INFO -   - 结果保存至: results/fixed_laq_results.jsonl
