#!/bin/bash

# LAPA环境激活脚本
# 使用方法: source activate_lapa_env.sh

echo "激活LAPA训练环境..."

# 激活conda环境
source ~/anaconda3/etc/profile.d/conda.sh
conda activate lapa_training

# 设置PYTHONPATH
export PYTHONPATH="${PYTHONPATH}:/home/<USER>/johnny_ws/lapa_ws/LAPA"

# 设置工作目录
cd /home/<USER>/johnny_ws/lapa_ws

# 验证环境
echo "验证环境配置..."
python -c "
import jax, flax
from latent_pretraining.delta_llama import VideoLLaMAConfig
print(f'✅ LAPA环境已激活')
print(f'   JAX版本: {jax.__version__}')
print(f'   Flax版本: {flax.__version__}')
print(f'   工作目录: $(pwd)')
print(f'   Python路径已设置')
" 2>/dev/null

if [ $? -eq 0 ]; then
    echo "🎉 LAPA环境激活成功！"
    echo ""
    echo "可用命令："
    echo "  bash scripts/latent_action_pretrain_lapa.sh  # 开始训练"
    echo "  python scripts/verify_lapa_environment.py   # 验证环境"
    echo "  python scripts/test_lapa_laq_model.py       # 测试模型"
else
    echo "❌ 环境激活失败，请检查配置"
fi
