#!/usr/bin/env python3
"""
采样方法对比工具
比较固定间隔采样vs关键帧采样的效果
"""

import os
import json
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import argparse
from typing import Dict, List, Tuple
import cv2
from PIL import Image

class SamplingMethodComparator:
    def __init__(self):
        """采样方法对比器"""
        pass
    
    def load_metadata(self, metadata_path: str) -> Dict:
        """加载预处理元数据"""
        with open(metadata_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    
    def load_frame_pairs(self, frame_pairs_path: str) -> List[Dict]:
        """加载帧对数据"""
        with open(frame_pairs_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    
    def calculate_frame_difference(self, img1_path: str, img2_path: str) -> float:
        """计算两帧之间的差异"""
        try:
            img1 = cv2.imread(img1_path, cv2.IMREAD_GRAYSCALE)
            img2 = cv2.imread(img2_path, cv2.IMREAD_GRAYSCALE)
            
            if img1 is None or img2 is None:
                return 0.0
            
            # 确保图像大小一致
            if img1.shape != img2.shape:
                img2 = cv2.resize(img2, (img1.shape[1], img1.shape[0]))
            
            # 计算帧差
            diff = cv2.absdiff(img1, img2)
            return np.mean(diff) / 255.0
            
        except Exception as e:
            print(f"计算帧差时出错: {e}")
            return 0.0
    
    def analyze_frame_pair_quality(self, frame_pairs: List[Dict]) -> Dict:
        """分析帧对质量"""
        print("分析帧对质量...")
        
        frame_diffs = []
        frame_intervals = []
        methods = []
        
        for pair in frame_pairs:
            # 计算帧差
            diff = self.calculate_frame_difference(pair['first_frame'], pair['second_frame'])
            frame_diffs.append(diff)
            
            # 获取帧间隔
            if 'frame_interval' in pair:
                frame_intervals.append(pair['frame_interval'])
            elif 'frame_indices' in pair and len(pair['frame_indices']) == 2:
                interval = pair['frame_indices'][1] - pair['frame_indices'][0]
                frame_intervals.append(interval)
            else:
                frame_intervals.append(5)  # 默认间隔
            
            # 获取方法
            methods.append(pair.get('method', 'unknown'))
        
        return {
            'frame_diffs': frame_diffs,
            'frame_intervals': frame_intervals,
            'methods': methods,
            'total_pairs': len(frame_pairs),
            'avg_frame_diff': np.mean(frame_diffs),
            'std_frame_diff': np.std(frame_diffs),
            'avg_interval': np.mean(frame_intervals),
            'std_interval': np.std(frame_intervals)
        }
    
    def compare_methods(self, results_dir: str) -> Dict:
        """比较不同采样方法的结果"""
        methods = ['fixed', 'frame_diff', 'optical_flow', 'hybrid']
        comparison_results = {}
        
        for method in methods:
            metadata_path = os.path.join(results_dir, f'preprocessing_metadata_{method}.json')
            frame_pairs_path = os.path.join(results_dir, f'frame_pairs_{method}.json')
            
            if os.path.exists(metadata_path) and os.path.exists(frame_pairs_path):
                print(f"\n分析方法: {method}")
                
                # 加载数据
                metadata = self.load_metadata(metadata_path)
                frame_pairs = self.load_frame_pairs(frame_pairs_path)
                
                # 分析质量
                quality_analysis = self.analyze_frame_pair_quality(frame_pairs)
                
                comparison_results[method] = {
                    'metadata': metadata,
                    'quality_analysis': quality_analysis
                }
                
                print(f"  总帧对数: {quality_analysis['total_pairs']}")
                print(f"  平均帧差: {quality_analysis['avg_frame_diff']:.4f}")
                print(f"  平均间隔: {quality_analysis['avg_interval']:.2f}")
            else:
                print(f"方法 {method} 的结果文件不存在")
        
        return comparison_results
    
    def visualize_comparison(self, comparison_results: Dict, save_dir: str):
        """可视化比较结果"""
        os.makedirs(save_dir, exist_ok=True)
        
        # 设置绘图风格
        plt.style.use('seaborn-v0_8')
        sns.set_palette("husl")
        
        # 1. 帧对数量对比
        plt.figure(figsize=(12, 8))
        
        methods = list(comparison_results.keys())
        pair_counts = [comparison_results[method]['quality_analysis']['total_pairs'] 
                      for method in methods]
        avg_diffs = [comparison_results[method]['quality_analysis']['avg_frame_diff'] 
                    for method in methods]
        
        # 子图1: 帧对数量
        plt.subplot(2, 2, 1)
        bars = plt.bar(methods, pair_counts, alpha=0.7)
        plt.title('不同方法生成的帧对数量')
        plt.ylabel('帧对数量')
        plt.xticks(rotation=45)
        
        # 在柱状图上添加数值标签
        for bar, count in zip(bars, pair_counts):
            plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + max(pair_counts)*0.01,
                    str(count), ha='center', va='bottom')
        
        # 子图2: 平均帧差
        plt.subplot(2, 2, 2)
        bars = plt.bar(methods, avg_diffs, alpha=0.7, color='orange')
        plt.title('平均帧差对比')
        plt.ylabel('平均帧差')
        plt.xticks(rotation=45)
        
        for bar, diff in zip(bars, avg_diffs):
            plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + max(avg_diffs)*0.01,
                    f'{diff:.4f}', ha='center', va='bottom')
        
        # 子图3: 帧差分布对比
        plt.subplot(2, 2, 3)
        for method in methods:
            if method in comparison_results:
                diffs = comparison_results[method]['quality_analysis']['frame_diffs']
                plt.hist(diffs, bins=30, alpha=0.5, label=method, density=True)
        
        plt.title('帧差分布对比')
        plt.xlabel('帧差值')
        plt.ylabel('密度')
        plt.legend()
        
        # 子图4: 效率对比（帧对数量 vs 平均质量）
        plt.subplot(2, 2, 4)
        plt.scatter(pair_counts, avg_diffs, s=100, alpha=0.7)
        
        for i, method in enumerate(methods):
            plt.annotate(method, (pair_counts[i], avg_diffs[i]), 
                        xytext=(5, 5), textcoords='offset points')
        
        plt.title('效率 vs 质量')
        plt.xlabel('帧对数量')
        plt.ylabel('平均帧差')
        plt.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(os.path.join(save_dir, 'sampling_methods_comparison.png'), 
                   dpi=300, bbox_inches='tight')
        plt.show()
        
        # 2. 详细的帧间隔分析
        plt.figure(figsize=(15, 5))
        
        for i, method in enumerate(methods):
            if method in comparison_results:
                plt.subplot(1, len(methods), i+1)
                intervals = comparison_results[method]['quality_analysis']['frame_intervals']
                plt.hist(intervals, bins=20, alpha=0.7, edgecolor='black')
                plt.title(f'{method}\n帧间隔分布')
                plt.xlabel('帧间隔')
                plt.ylabel('频次')
                plt.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(os.path.join(save_dir, 'frame_intervals_comparison.png'), 
                   dpi=300, bbox_inches='tight')
        plt.show()
    
    def generate_report(self, comparison_results: Dict, save_path: str):
        """生成对比报告"""
        report = []
        report.append("# 视频帧采样方法对比报告\n")
        report.append("## 总体统计\n")
        
        # 创建对比表格
        report.append("| 方法 | 帧对数量 | 平均帧差 | 帧差标准差 | 平均间隔 | 间隔标准差 |")
        report.append("|------|----------|----------|------------|----------|------------|")
        
        for method, results in comparison_results.items():
            qa = results['quality_analysis']
            report.append(f"| {method} | {qa['total_pairs']} | {qa['avg_frame_diff']:.4f} | "
                         f"{qa['std_frame_diff']:.4f} | {qa['avg_interval']:.2f} | {qa['std_interval']:.2f} |")
        
        report.append("\n## 分析结论\n")
        
        # 找出最佳方法
        best_quality_method = max(comparison_results.keys(), 
                                 key=lambda x: comparison_results[x]['quality_analysis']['avg_frame_diff'])
        most_efficient_method = min(comparison_results.keys(), 
                                   key=lambda x: comparison_results[x]['quality_analysis']['total_pairs'])
        
        report.append(f"- **最高质量方法**: {best_quality_method} (平均帧差: "
                     f"{comparison_results[best_quality_method]['quality_analysis']['avg_frame_diff']:.4f})")
        report.append(f"- **最高效率方法**: {most_efficient_method} (帧对数量: "
                     f"{comparison_results[most_efficient_method]['quality_analysis']['total_pairs']})")
        
        # 计算数据减少比例
        if 'fixed' in comparison_results:
            fixed_count = comparison_results['fixed']['quality_analysis']['total_pairs']
            for method, results in comparison_results.items():
                if method != 'fixed':
                    current_count = results['quality_analysis']['total_pairs']
                    reduction = (fixed_count - current_count) / fixed_count * 100
                    report.append(f"- **{method}方法数据减少**: {reduction:.1f}% "
                                 f"({fixed_count} → {current_count})")
        
        report.append("\n## 推荐\n")
        report.append("基于分析结果，推荐使用以下策略：")
        report.append("1. 如果追求最高质量，使用 **hybrid** 方法")
        report.append("2. 如果需要平衡质量和效率，使用 **frame_diff** 方法")
        report.append("3. 如果计算资源有限，使用 **fixed** 方法作为基准")
        
        # 保存报告
        with open(save_path, 'w', encoding='utf-8') as f:
            f.write('\n'.join(report))
        
        print(f"对比报告已保存到: {save_path}")

def main():
    parser = argparse.ArgumentParser(description='采样方法对比工具')
    parser.add_argument('--results_dir', type=str, required=True, 
                       help='包含不同方法结果的目录')
    parser.add_argument('--output_dir', type=str, default='comparison_results',
                       help='对比结果输出目录')
    
    args = parser.parse_args()
    
    # 创建对比器
    comparator = SamplingMethodComparator()
    
    # 比较方法
    comparison_results = comparator.compare_methods(args.results_dir)
    
    if not comparison_results:
        print("未找到可比较的结果文件")
        return
    
    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)
    
    # 可视化对比
    comparator.visualize_comparison(comparison_results, args.output_dir)
    
    # 生成报告
    report_path = os.path.join(args.output_dir, 'sampling_comparison_report.md')
    comparator.generate_report(comparison_results, report_path)
    
    print(f"\n对比分析完成！")
    print(f"结果保存在: {args.output_dir}")

if __name__ == "__main__":
    main()
