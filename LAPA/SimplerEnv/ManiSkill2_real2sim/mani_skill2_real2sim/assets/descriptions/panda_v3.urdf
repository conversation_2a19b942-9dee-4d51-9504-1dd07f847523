<?xml version="1.0" ?>
<!-- =================================================================================== -->
<!-- |    This document was autogenerated by xacro from panda_arm.urdf.xacro           | -->
<!-- |    EDITING THIS FILE BY HAND IS NOT RECOMMENDED                                 | -->
<!-- =================================================================================== -->
<robot name="panda">
  <link name="panda_link0">
    <visual>
      <geometry>
        <mesh filename="franka_description/meshes/visual/link0.dae"/>
      </geometry>
    </visual>
    <collision>
      <geometry>
        <mesh filename="franka_description/meshes/collision/link0.stl"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="-0.041018 -0.00014 0.049974"/>
      <mass value="0.629769"/>
      <inertia ixx="0.00315" ixy="8.2904e-07" ixz="0.00015" iyy="0.00388" iyz="8.2299e-06" izz="0.004285"/>
    </inertial>
  </link>
  <link name="panda_link1">
    <visual>
      <geometry>
        <mesh filename="franka_description/meshes/visual/link1.dae"/>
      </geometry>
    </visual>
    <collision>
      <geometry>
        <mesh filename="franka_description/meshes/collision/link1.stl"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="0.003875 0.002081 -0.04762"/>
      <mass value="4.970684"/>
      <inertia ixx="0.70337" ixy="-0.000139" ixz="0.006772" iyy="0.70661" iyz="0.019169" izz="0.009117"/>
    </inertial>
  </link>
  <joint name="panda_joint1" type="revolute">
    <safety_controller k_position="100.0" k_velocity="40.0" soft_lower_limit="-2.8973" soft_upper_limit="2.8973"/>
    <origin rpy="0 0 0" xyz="0 0 0.333"/>
    <parent link="panda_link0"/>
    <child link="panda_link1"/>
    <axis xyz="0 0 1"/>
    <limit effort="87" lower="-2.8973" upper="2.8973" velocity="2.1750"/>
    <dynamics D="1" K="7000" damping="0.003" friction="0.0" mu_coulomb="0" mu_viscous="16"/>
  </joint>
  <link name="panda_link2">
    <visual>
      <geometry>
        <mesh filename="franka_description/meshes/visual/link2.dae"/>
      </geometry>
    </visual>
    <collision>
      <geometry>
        <mesh filename="franka_description/meshes/collision/link2.stl"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="-0.003141 -0.02872  0.003495"/>
      <mass value="0.646926"/>
      <inertia ixx="0.007962" ixy="-0.003925" ixz="0.010254" iyy="0.02811" iyz="0.000704" izz="0.025995"/>
    </inertial>
  </link>
  <joint name="panda_joint2" type="revolute">
    <safety_controller k_position="100.0" k_velocity="40.0" soft_lower_limit="-1.7628" soft_upper_limit="1.7628"/>
    <origin rpy="-1.5707963267948966 0 0" xyz="0 0 0"/>
    <parent link="panda_link1"/>
    <child link="panda_link2"/>
    <axis xyz="0 0 1"/>
    <limit effort="87" lower="-1.7628" upper="1.7628" velocity="2.1750"/>
    <dynamics D="1" K="7000" damping="0.003" friction="0.0" mu_coulomb="0" mu_viscous="16"/>
  </joint>
  <link name="panda_link3">
    <visual>
      <geometry>
        <mesh filename="franka_description/meshes/visual/link3.dae"/>
      </geometry>
    </visual>
    <collision>
      <geometry>
        <mesh filename="franka_description/meshes/collision/link3.stl"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="2.7518e-02 3.9252e-02 -6.6502e-02"/>
      <mass value="3.228604"/>
      <inertia ixx="0.037242" ixy="-0.004761" ixz="-0.011396" iyy="0.036155" iyz="-0.012805" izz="0.01083"/>
    </inertial>
  </link>
  <joint name="panda_joint3" type="revolute">
    <safety_controller k_position="100.0" k_velocity="40.0" soft_lower_limit="-2.8973" soft_upper_limit="2.8973"/>
    <origin rpy="1.5707963267948966 0 0" xyz="0 -0.316 0"/>
    <parent link="panda_link2"/>
    <child link="panda_link3"/>
    <axis xyz="0 0 1"/>
    <limit effort="87" lower="-2.8973" upper="2.8973" velocity="2.1750"/>
    <dynamics D="1" K="7000" damping="0.003" friction="0.0" mu_coulomb="0" mu_viscous="16"/>
  </joint>
  <link name="panda_link4">
    <visual>
      <geometry>
        <mesh filename="franka_description/meshes/visual/link4.dae"/>
      </geometry>
    </visual>
    <collision>
      <geometry>
        <mesh filename="franka_description/meshes/collision/link4.stl"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="-5.317e-02 1.04419e-01 2.7454e-02"/>
      <mass value="3.587895"/>
      <inertia ixx="0.025853" ixy="0.007796" ixz="-0.001332" iyy="0.019552" iyz="0.008641" izz="0.028323"/>
    </inertial>
  </link>
  <joint name="panda_joint4" type="revolute">
    <safety_controller k_position="100.0" k_velocity="40.0" soft_lower_limit="-3.0718" soft_upper_limit="-0.0698"/>
    <origin rpy="1.5707963267948966 0 0" xyz="0.0825 0 0"/>
    <parent link="panda_link3"/>
    <child link="panda_link4"/>
    <axis xyz="0 0 1"/>
    <limit effort="87" lower="-3.0718" upper="-0.0698" velocity="2.1750"/>
    <dynamics D="1" K="7000" damping="0.003" friction="0.0" mu_coulomb="0" mu_viscous="16"/>
  </joint>
  <link name="panda_link5">
    <visual>
      <geometry>
        <mesh filename="franka_description/meshes/visual/link5.dae"/>
      </geometry>
    </visual>
    <collision>
      <geometry>
        <mesh filename="franka_description/meshes/collision/link5.stl"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="-1.1953e-02 4.1065e-02 -3.8437e-02"/>
      <mass value="1.225946"/>
      <inertia ixx="0.035549" ixy="-0.002117" ixz="-0.004037" iyy="0.029474" iyz="0.000229" izz="0.008627"/>
    </inertial>
  </link>
  <joint name="panda_joint5" type="revolute">
    <safety_controller k_position="100.0" k_velocity="40.0" soft_lower_limit="-2.8973" soft_upper_limit="2.8973"/>
    <origin rpy="-1.5707963267948966 0 0" xyz="-0.0825 0.384 0"/>
    <parent link="panda_link4"/>
    <child link="panda_link5"/>
    <axis xyz="0 0 1"/>
    <limit effort="12" lower="-2.8973" upper="2.8973" velocity="2.6100"/>
    <dynamics D="1" K="7000" damping="0.003" friction="0.0" mu_coulomb="0" mu_viscous="16"/>
  </joint>
  <link name="panda_link6">
    <visual>
      <geometry>
        <mesh filename="franka_description/meshes/visual/link6.dae"/>
      </geometry>
    </visual>
    <collision>
      <geometry>
        <mesh filename="franka_description/meshes/collision/link6.stl"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="6.0149e-02 -1.4117e-02 -1.0517e-02"/>
      <mass value="1.666555"/>
      <inertia ixx="0.001964" ixy="0.000109" ixz="-0.001158" iyy="0.004354" iyz="0.000341" izz="0.005433"/>
    </inertial>
  </link>
  <joint name="panda_joint6" type="revolute">
    <safety_controller k_position="100.0" k_velocity="40.0" soft_lower_limit="-0.0175" soft_upper_limit="3.7525"/>
    <origin rpy="1.5707963267948966 0 0" xyz="0 0 0"/>
    <parent link="panda_link5"/>
    <child link="panda_link6"/>
    <axis xyz="0 0 1"/>
    <limit effort="12" lower="-0.0175" upper="3.7525" velocity="2.6100"/>
    <dynamics D="1" K="7000" damping="0.003" friction="0.0" mu_coulomb="0" mu_viscous="16"/>
  </joint>
  <link name="panda_link7">
    <visual>
      <geometry>
        <mesh filename="franka_description/meshes/visual/link7.dae"/>
      </geometry>
    </visual>
    <collision>
      <geometry>
        <mesh filename="franka_description/meshes/collision/link7.stl"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="1.0517e-02 -4.252e-03 6.1597e-02"/>
      <mass value="0.735522"/>
      <inertia ixx="0.012516" ixy="-0.000428" ixz="-0.001196" iyy="0.010027" iyz="-0.000741" izz="0.004815"/>
    </inertial>
  </link>
  <joint name="panda_joint7" type="revolute">
    <safety_controller k_position="100.0" k_velocity="40.0" soft_lower_limit="-2.8973" soft_upper_limit="2.8973"/>
    <origin rpy="1.5707963267948966 0 0" xyz="0.088 0 0"/>
    <parent link="panda_link6"/>
    <child link="panda_link7"/>
    <axis xyz="0 0 1"/>
    <limit effort="12" lower="-2.8973" upper="2.8973" velocity="2.6100"/>
    <dynamics D="1" K="7000" damping="0.003" friction="0.0" mu_coulomb="0" mu_viscous="16"/>
  </joint>
  <link name="panda_link8"/>
  <joint name="panda_joint8" type="fixed">
    <origin rpy="0 0 0" xyz="0 0 0.107"/>
    <parent link="panda_link7"/>
    <child link="panda_link8"/>
  </joint>
  <joint name="panda_hand_joint" type="fixed">
    <parent link="panda_link8"/>
    <child link="panda_hand"/>
    <origin rpy="0 0 -0.7853981633974483" xyz="0 0 0"/>
  </joint>
  <link name="panda_hand">
    <visual>
      <geometry>
        <mesh filename="franka_description/meshes/visual/hand.dae"/>
      </geometry>
    </visual>
    <collision>
      <geometry>
        <mesh filename="franka_description/meshes/collision/hand.stl"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="-0.01 0 0.03"/>
      <mass value="0.73"/>
      <inertia ixx="0.001" ixy="0" ixz="0" iyy="0.0025" iyz="0" izz="0.0017"/>
    </inertial>
  </link>
  <!-- Define the hand_tcp frame -->
  <link name="panda_hand_tcp"/>
  <joint name="panda_hand_tcp_joint" type="fixed">
    <origin rpy="0 0 0" xyz="0 0 0.1034"/>
    <parent link="panda_hand"/>
    <child link="panda_hand_tcp"/>
  </joint>
  <link name="panda_leftfinger">
    <visual>
      <geometry>
        <mesh filename="franka_description/meshes/visual/finger.dae"/>
      </geometry>
    </visual>
    <!-- screw mount -->
    <collision>
      <origin rpy="0 0 0" xyz="0 18.5e-3 11e-3"/>
      <geometry>
        <box size="22e-3 15e-3 20e-3"/>
      </geometry>
    </collision>
    <!-- cartriage sledge -->
    <collision>
      <origin rpy="0 0 0" xyz="0 6.8e-3 2.2e-3"/>
      <geometry>
        <box size="22e-3 8.8e-3 3.8e-3"/>
      </geometry>
    </collision>
    <!-- diagonal finger -->
    <collision>
      <origin rpy="0.5235987755982988 0 0" xyz="0 15.9e-3 28.35e-3"/>
      <geometry>
        <box size="17.5e-3 7e-3 23.5e-3"/>
      </geometry>
    </collision>
    <!-- rubber tip with which to grasp -->
    <collision>
      <origin rpy="0 0 0" xyz="0 7.58e-3 45.25e-3"/>
      <geometry>
        <box size="17.5e-3 15.2e-3 18.5e-3"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <mass value="0.015"/>
      <inertia ixx="2.3749999999999997e-06" ixy="0" ixz="0" iyy="2.3749999999999997e-06" iyz="0" izz="7.5e-07"/>
    </inertial>
  </link>
  <link name="panda_rightfinger">
    <visual>
      <origin rpy="0 0 3.141592653589793" xyz="0 0 0"/>
      <geometry>
        <mesh filename="franka_description/meshes/visual/finger.dae"/>
      </geometry>
    </visual>
    <!-- screw mount -->
    <collision>
      <origin rpy="0 0 0" xyz="0 -18.5e-3 11e-3"/>
      <geometry>
        <box size="22e-3 15e-3 20e-3"/>
      </geometry>
    </collision>
    <!-- cartriage sledge -->
    <collision>
      <origin rpy="0 0 0" xyz="0 -6.8e-3 2.2e-3"/>
      <geometry>
        <box size="22e-3 8.8e-3 3.8e-3"/>
      </geometry>
    </collision>
    <!-- diagonal finger -->
    <collision>
      <origin rpy="-0.5235987755982988 0 0" xyz="0 -15.9e-3 28.35e-3"/>
      <geometry>
        <box size="17.5e-3 7e-3 23.5e-3"/>
      </geometry>
    </collision>
    <!-- rubber tip with which to grasp -->
    <collision>
      <origin rpy="0 0 0" xyz="0 -7.58e-3 45.25e-3"/>
      <geometry>
        <box size="17.5e-3 15.2e-3 18.5e-3"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <mass value="0.015"/>
      <inertia ixx="2.3749999999999997e-06" ixy="0" ixz="0" iyy="2.3749999999999997e-06" iyz="0" izz="7.5e-07"/>
    </inertial>
  </link>
  <joint name="panda_finger_joint1" type="prismatic">
    <parent link="panda_hand"/>
    <child link="panda_leftfinger"/>
    <origin rpy="0 0 0" xyz="0 0 0.0584"/>
    <axis xyz="0 1 0"/>
    <limit effort="100" lower="0.0" upper="0.04" velocity="0.2"/>
    <dynamics damping="0.3"/>
  </joint>
  <joint name="panda_finger_joint2" type="prismatic">
    <parent link="panda_hand"/>
    <child link="panda_rightfinger"/>
    <origin rpy="0 0 0" xyz="0 0 0.0584"/>
    <axis xyz="0 -1 0"/>
    <limit effort="100" lower="0.0" upper="0.04" velocity="0.2"/>
    <mimic joint="panda_finger_joint1"/>
    <dynamics damping="0.3"/>
  </joint>
  
  <!--  camera mount-->
  <joint name="realsense_joint" type="fixed">
    <origin rpy="0 -1.5707 3.1415" xyz="0.035 0 0.036"/>
    <parent link="panda_hand"/>
    <child link="camera_base_link"/>
  </joint>
  <link name="camera_base_link"/>
  <joint name="camera_link_joint" type="fixed">
    <origin rpy="0 0 0" xyz="0 0.02 0.0115"/>
    <parent link="camera_base_link"/>
    <child link="camera_link"/>
  </joint>
  <link name="camera_link">
    <!-- <visual>
      <origin rpy="1.57079632679 0 1.57079632679" xyz="0.00987 -0.02 0"/>
      <geometry>
        <mesh filename="franka_description/meshes/visual/d435.dae" />
      </geometry>
    </visual> -->
    <visual>
      <origin rpy="0 0 0" xyz="0 -0.02 0"/>
      <geometry>
        <box size="0.02005 0.099 0.023"/>
      </geometry>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 -0.02 0"/>
      <geometry>
        <box size="0.02005 0.099 0.023"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="0.564"/>
      <origin xyz="0 0 0"/>
      <inertia ixx="0.003881243" ixy="0.0" ixz="0.0" iyy="0.000498940" iyz="0.0" izz="0.003879257"/>
    </inertial>
  </link>
</robot>
