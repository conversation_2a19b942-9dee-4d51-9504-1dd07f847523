{"bridge_carrot_generated": {"bbox": {"min": [-0.0575, -0.016, -0.019], "max": [0.0575, 0.016, 0.019]}, "scales": [1.0], "density": 700}, "bridge_carrot_generated_modified": {"bbox": {"min": [-0.0575, -0.016, -0.019], "max": [0.0575, 0.016, 0.019]}, "scales": [1.0], "density": 700}, "bridge_plate_objaverse": {"bbox": {"min": [-0.05, -0.05, -0.003], "max": [0.05, 0.05, 0.003]}, "scales": [1.0], "density": 1000}, "bridge_plate_objaverse_larger": {"bbox": {"min": [-0.055, -0.055, -0.003], "max": [0.055, 0.055, 0.003]}, "scales": [1.0], "density": 1000}, "bridge_spoon_generated_modified": {"bbox": {"min": [-0.07, -0.0173, -0.013021], "max": [0.07, 0.0173, 0.013021]}, "scales": [1.0], "density": 1200}, "table_cloth_generated": {"bbox": {"min": [-0.05, -0.05, -0.0125], "max": [0.05, 0.05, 0.0125]}, "scales": [1.0], "density": 330}, "table_cloth_generated_shorter": {"bbox": {"min": [-0.05, -0.05, -0.005], "max": [0.05, 0.05, 0.005]}, "scales": [1.0], "density": 330}, "green_cube_3cm": {"bbox": {"min": [-0.015, -0.015, -0.015], "max": [0.015, 0.015, 0.015]}, "scales": [1.0], "density": 1000}, "yellow_cube_3cm": {"bbox": {"min": [-0.015, -0.015, -0.015], "max": [0.015, 0.015, 0.015]}, "scales": [1.0], "density": 1000}, "eggplant": {"bbox": {"min": [-0.03915, -0.0187, -0.019], "max": [0.03915, 0.0187, 0.019]}, "scales": [1.0], "density": 400}, "sink": {"bbox": {"min": [-0.1345, -0.1965, 0], "max": [0.1345, 0.1965, 0.115]}, "scales": [1.0], "density": 100000}, "dummy_sink_target_plane": {"bbox": {"min": [-0.065, -0.04, -0.001], "max": [0.065, 0.04, 0.001]}, "scales": [1.0], "density": 100000}}