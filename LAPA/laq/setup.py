from setuptools import setup, find_packages

setup(
  name = 'laq',
  packages=find_packages(include=["laq", "laq.*"]),
  version = '0.4.2',
  license='MIT',
  description = '<PERSON>ena<PERSON> - Pytorch',
  author = '<PERSON>',
  author_email = '<EMAIL>',
  long_description_content_type = 'text/markdown',
  url = 'https://github.com/lucidrains/phenaki-pytorch',
  keywords = [
    'artificial intelligence',
    'deep learning',
    'transformers',
    'attention mechanisms',
    'text-to-video'
  ],
  install_requires = [
    "accelerate>=0.25.0",
    'beartype',
    'einops>=0.7',
    'ema-pytorch>=0.2.2',
    'opencv-python',
    'pillow',
    'numpy',
    "torch==2.2.0",
    "torchvision>=0.16.0",
    "torchaudio",
    'torchtyping',
    'tqdm',
    'vector-quantize-pytorch>=1.11.8',
    'wandb',
    'webdataset',
    "draccus==0.8.0",
    "huggingface_hub",
    "json-numpy",
    "jsonlines",
    "matplotlib",
    "peft==0.11.1",
    "protobuf",
    "rich",
    "sentencepiece==0.1.99",
    "timm==0.9.10",
    "tokenizers==0.19.1",
    "transformers==4.40.1",
    "tensorflow==2.15.0",
    "tensorflow_datasets==4.9.3",
    "tensorflow_graphics==2021.12.3",
    "dlimp @ git+https://github.com/moojink/dlimp_openvla"
  ],
  classifiers=[
    'Development Status :: 4 - Beta',
    'Intended Audience :: Developers',
    'Topic :: Scientific/Engineering :: Artificial Intelligence',
    'License :: OSI Approved :: MIT License',
    'Programming Language :: Python :: 3.6',
  ],
)
