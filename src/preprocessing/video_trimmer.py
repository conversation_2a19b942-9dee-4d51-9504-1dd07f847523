#!/usr/bin/env python3
"""
视频时长处理脚本
对于大于3秒的视频，截取倒数3秒钟，直接覆盖原视频
"""

import os
import cv2
import glob
import argparse
from pathlib import Path
from tqdm import tqdm
import tempfile
import shutil

class VideoTrimmer:
    def __init__(self, target_duration=3.0):
        """
        视频裁剪器
        
        Args:
            target_duration: 目标时长（秒），默认3秒
        """
        self.target_duration = target_duration
        
    def get_video_info(self, video_path):
        """获取视频信息"""
        cap = cv2.VideoCapture(video_path)
        
        if not cap.isOpened():
            return None
        
        fps = cap.get(cv2.CAP_PROP_FPS)
        frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        duration = frame_count / fps if fps > 0 else 0
        
        cap.release()
        
        return {
            'fps': fps,
            'frame_count': frame_count,
            'width': width,
            'height': height,
            'duration': duration
        }
    
    def trim_video_last_n_seconds(self, input_path, output_path, duration_seconds):
        """截取视频的倒数N秒"""
        # 获取视频信息
        video_info = self.get_video_info(input_path)
        if video_info is None:
            print(f"无法读取视频信息: {input_path}")
            return False
        
        total_duration = video_info['duration']
        fps = video_info['fps']
        width = video_info['width']
        height = video_info['height']
        
        # 如果视频时长小于等于目标时长，不需要处理
        if total_duration <= duration_seconds:
            print(f"视频时长 {total_duration:.2f}s <= {duration_seconds}s，跳过: {os.path.basename(input_path)}")
            return True
        
        # 计算开始时间和帧数
        start_time = total_duration - duration_seconds
        start_frame = int(start_time * fps)
        target_frame_count = int(duration_seconds * fps)
        
        print(f"处理视频: {os.path.basename(input_path)}")
        print(f"  原时长: {total_duration:.2f}s ({video_info['frame_count']}帧)")
        print(f"  截取: 从 {start_time:.2f}s 开始，时长 {duration_seconds}s ({target_frame_count}帧)")
        
        # 打开输入视频
        cap = cv2.VideoCapture(input_path)
        if not cap.isOpened():
            print(f"无法打开视频: {input_path}")
            return False
        
        # 设置视频编解码器
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        
        # 创建输出视频写入器
        out = cv2.VideoWriter(output_path, fourcc, fps, (width, height))
        
        if not out.isOpened():
            print(f"无法创建输出视频: {output_path}")
            cap.release()
            return False
        
        # 跳转到开始帧
        cap.set(cv2.CAP_PROP_POS_FRAMES, start_frame)
        
        frames_written = 0
        frames_to_write = target_frame_count
        
        # 读取并写入帧
        with tqdm(total=frames_to_write, desc="写入帧", leave=False) as pbar:
            while frames_written < frames_to_write:
                ret, frame = cap.read()
                if not ret:
                    break
                
                out.write(frame)
                frames_written += 1
                pbar.update(1)
        
        # 释放资源
        cap.release()
        out.release()
        
        print(f"  完成: 写入 {frames_written} 帧")
        return frames_written > 0
    
    def process_single_video(self, video_path):
        """处理单个视频文件"""
        try:
            # 创建临时文件
            temp_dir = tempfile.mkdtemp()
            temp_output = os.path.join(temp_dir, f"temp_{os.path.basename(video_path)}")
            
            # 截取视频
            success = self.trim_video_last_n_seconds(video_path, temp_output, self.target_duration)
            
            if success and os.path.exists(temp_output):
                # 检查输出文件大小
                if os.path.getsize(temp_output) > 0:
                    # 用临时文件覆盖原文件
                    shutil.move(temp_output, video_path)
                    print(f"✓ 成功处理并覆盖: {os.path.basename(video_path)}")
                    return True
                else:
                    print(f"✗ 输出文件为空: {os.path.basename(video_path)}")
                    return False
            else:
                print(f"✗ 处理失败: {os.path.basename(video_path)}")
                return False
                
        except Exception as e:
            print(f"✗ 处理视频时出错 {os.path.basename(video_path)}: {e}")
            return False
        finally:
            # 清理临时目录
            if 'temp_dir' in locals() and os.path.exists(temp_dir):
                shutil.rmtree(temp_dir, ignore_errors=True)
    
    def process_directory(self, input_dir, recursive=True):
        """处理目录中的所有视频文件"""
        print(f"扫描目录: {input_dir}")
        
        # 支持的视频格式
        video_extensions = ['*.mp4', '*.avi', '*.mov', '*.mkv', '*.wmv', '*.flv']
        
        video_files = []
        for ext in video_extensions:
            if recursive:
                pattern = os.path.join(input_dir, '**', ext)
                video_files.extend(glob.glob(pattern, recursive=True))
            else:
                pattern = os.path.join(input_dir, ext)
                video_files.extend(glob.glob(pattern))
        
        if not video_files:
            print("未找到视频文件")
            return
        
        print(f"找到 {len(video_files)} 个视频文件")
        
        # 统计信息
        processed_count = 0
        skipped_count = 0
        failed_count = 0
        
        # 处理每个视频
        for video_path in tqdm(video_files, desc="处理视频"):
            # 获取视频信息以决定是否需要处理
            video_info = self.get_video_info(video_path)
            
            if video_info is None:
                print(f"✗ 无法读取视频信息: {os.path.basename(video_path)}")
                failed_count += 1
                continue
            
            if video_info['duration'] <= self.target_duration:
                skipped_count += 1
                continue
            
            # 处理视频
            if self.process_single_video(video_path):
                processed_count += 1
            else:
                failed_count += 1
        
        # 输出统计结果
        print(f"\n处理完成!")
        print(f"总文件数: {len(video_files)}")
        print(f"成功处理: {processed_count}")
        print(f"跳过(≤{self.target_duration}s): {skipped_count}")
        print(f"处理失败: {failed_count}")
        
        return {
            'total': len(video_files),
            'processed': processed_count,
            'skipped': skipped_count,
            'failed': failed_count
        }
    
    def process_file_list(self, file_list):
        """处理文件列表"""
        print(f"处理 {len(file_list)} 个视频文件")
        
        processed_count = 0
        skipped_count = 0
        failed_count = 0
        
        for video_path in tqdm(file_list, desc="处理视频"):
            if not os.path.exists(video_path):
                print(f"✗ 文件不存在: {video_path}")
                failed_count += 1
                continue
            
            # 获取视频信息
            video_info = self.get_video_info(video_path)
            
            if video_info is None:
                print(f"✗ 无法读取视频信息: {os.path.basename(video_path)}")
                failed_count += 1
                continue
            
            if video_info['duration'] <= self.target_duration:
                skipped_count += 1
                continue
            
            # 处理视频
            if self.process_single_video(video_path):
                processed_count += 1
            else:
                failed_count += 1
        
        # 输出统计结果
        print(f"\n处理完成!")
        print(f"总文件数: {len(file_list)}")
        print(f"成功处理: {processed_count}")
        print(f"跳过(≤{self.target_duration}s): {skipped_count}")
        print(f"处理失败: {failed_count}")
        
        return {
            'total': len(file_list),
            'processed': processed_count,
            'skipped': skipped_count,
            'failed': failed_count
        }

def main():
    parser = argparse.ArgumentParser(description='视频时长处理工具 - 截取大于3秒视频的倒数3秒')
    parser.add_argument('input', type=str, help='输入目录或视频文件路径')
    parser.add_argument('--duration', type=float, default=3.0, help='目标时长（秒），默认3秒')
    parser.add_argument('--recursive', action='store_true', help='递归处理子目录')
    parser.add_argument('--file-list', type=str, help='包含视频文件路径的文本文件（每行一个路径）')
    
    args = parser.parse_args()
    
    # 创建视频处理器
    trimmer = VideoTrimmer(target_duration=args.duration)
    
    print(f"视频时长处理工具")
    print(f"目标时长: {args.duration} 秒")
    print(f"处理策略: 对于大于 {args.duration} 秒的视频，截取倒数 {args.duration} 秒并覆盖原文件")
    print("=" * 60)
    
    if args.file_list:
        # 从文件列表读取
        if not os.path.exists(args.file_list):
            print(f"文件列表不存在: {args.file_list}")
            return
        
        with open(args.file_list, 'r', encoding='utf-8') as f:
            file_list = [line.strip() for line in f if line.strip()]
        
        trimmer.process_file_list(file_list)
        
    elif os.path.isfile(args.input):
        # 处理单个文件
        print(f"处理单个文件: {args.input}")
        
        video_info = trimmer.get_video_info(args.input)
        if video_info is None:
            print("无法读取视频信息")
            return
        
        if video_info['duration'] <= args.duration:
            print(f"视频时长 {video_info['duration']:.2f}s <= {args.duration}s，无需处理")
        else:
            if trimmer.process_single_video(args.input):
                print("处理成功")
            else:
                print("处理失败")
                
    elif os.path.isdir(args.input):
        # 处理目录
        trimmer.process_directory(args.input, recursive=args.recursive)
        
    else:
        print(f"输入路径不存在: {args.input}")

if __name__ == "__main__":
    main()
