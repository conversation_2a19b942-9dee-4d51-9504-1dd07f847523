# LAQ模型推理配置文件
# 该配置文件定义了LAQ模型推理的所有参数设置
# ✓ 已根据预训练模型laq_openx.pt的实际参数进行校正

# 模型配置 - 必须与预训练模型完全匹配的架构参数
model:
  # ========== 不可调整参数 (与预训练权重绑定) ==========
  dim: 1024                    # ✓ 不可调整 - 模型维度，与预训练权重匹配
  quant_dim: 32               # ✓ 不可调整 - 量化维度，与预训练权重匹配
  codebook_size: 8            # ✓ 不可调整 - 码本大小，与预训练权重匹配
  image_size: 256             # ✓ 不可调整 - 输入图像尺寸，与预训练权重匹配
  patch_size: 32              # ✓ 不可调整 - 图像块大小，与预训练权重匹配
  spatial_depth: 8            # ✓ 不可调整 - 空间Transformer层数，与预训练权重匹配
  temporal_depth: 8           # ✓ 不可调整 - 时间Transformer层数，与预训练权重匹配
  dim_head: 64                # ✓ 不可调整 - 注意力头维度，与预训练权重匹配
  heads: 16                   # ✓ 不可调整 - 注意力头数量，与预训练权重匹配
  code_seq_len: 4             # ✓ 不可调整 - 代码序列长度，与预训练权重匹配
  channels: 3                 # ✓ 不可调整 - 输入图像通道数，RGB三通道

  # ========== 可调整参数 (推理环境配置) ==========
  checkpoint_path: "models/laq_openx.pt"  # ✓ 可调整 - 预训练模型路径

# 推理配置 - 可根据硬件资源调整的参数
inference:
  batch_size: 32              # ✓ 可调整 - 批处理大小，根据GPU显存调整
  num_workers: 4              # ✓ 可调整 - 数据加载器工作进程数，根据CPU核心数调整
  pin_memory: true            # ✓ 可调整 - 内存锁定，GPU环境建议启用
  
# 数据配置
data:
  # 输入数据路径
  input_file: "data/smart_preprocessed_fixed/laq_dataset_hybrid.jsonl"
  
  # 输出结果路径
  output_file: "results/laq_inference_results.jsonl"
  
  # 数据预处理参数
  image_transforms:
    resize: 256               # 图像缩放尺寸
    normalize: false          # 是否标准化
    
# 日志配置
log_level: "INFO"             # 日志级别: DEBUG, INFO, WARNING, ERROR
log_file: "logs/laq_inference.log"  # 日志文件路径

# 性能配置
performance:
  use_cuda: true              # 是否使用CUDA（如果可用）
  mixed_precision: false      # 是否使用混合精度
  
# 输出配置
output:
  save_intermediate: false    # 是否保存中间结果
  result_format: "jsonl"      # 结果格式: jsonl, json
  include_metadata: true      # 是否包含元数据
  
# 高级配置
advanced:
  # 内存管理
  max_memory_usage: "24GB"     # 最大内存使用量
  
  # 错误处理
  continue_on_error: true     # 遇到错误时是否继续处理
  max_retries: 3              # 最大重试次数
  
  # 进度显示
  progress_bar: true          # 是否显示进度条
  update_frequency: 10        # 进度更新频率（批次）
