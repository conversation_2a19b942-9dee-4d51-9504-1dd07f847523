# LAQ大规模推理配置 - 适合处理大型数据集
# ❌ 重要修正：之前的codebook_size=512是错误的，会导致模型加载失败
# ✓ 现已修正为与预训练模型匹配的参数

# 模型配置 - 必须与预训练模型laq_openx.pt完全匹配
model:
  dim: 1024                   # ✓ 不可调整 - 模型维度，与预训练权重匹配
  quant_dim: 32              # ✓ 不可调整 - 量化维度，与预训练权重匹配
  codebook_size: 8           # ✓ 修正 - 从512改为8，与预训练权重匹配
  image_size: 256            # ✓ 不可调整 - 输入图像尺寸，与预训练权重匹配
  patch_size: 32             # ✓ 不可调整 - 图像块大小，与预训练权重匹配
  spatial_depth: 8           # ✓ 不可调整 - 空间Transformer层数，与预训练权重匹配
  temporal_depth: 8          # ✓ 不可调整 - 时间Transformer层数，与预训练权重匹配
  dim_head: 64               # ✓ 不可调整 - 注意力头维度，与预训练权重匹配
  heads: 16                  # ✓ 不可调整 - 注意力头数量，与预训练权重匹配
  code_seq_len: 4            # ✓ 不可调整 - 代码序列长度，与预训练权重匹配
  channels: 3                # ✓ 添加 - 输入图像通道数，RGB三通道
  checkpoint_path: "models/laq_openx.pt"

# 推理配置 - 优化大规模处理（可调整参数）
inference:
  batch_size: 128             # ✓ 可调整 - 大批处理大小，适合大规模数据处理
  num_workers: 8              # ✓ 可调整 - 更多工作进程以加速数据加载
  pin_memory: true            # ✓ 可调整 - 启用内存锁定以优化GPU传输

# 数据配置
data:
  input_file: "data/smart_preprocessed_fixed/laq_dataset_hybrid.jsonl"
  output_file: "results/laq_large_scale_results.jsonl"

# 日志配置
log_level: "INFO"
log_file: "logs/laq_large_scale_inference.log"

# 高级配置 - 大规模处理优化
advanced:
  continue_on_error: true     # 遇到错误继续处理
  max_retries: 3
  progress_bar: true
  update_frequency: 50        # 更频繁的进度更新
