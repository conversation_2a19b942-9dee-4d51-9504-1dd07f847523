# LAQ高质量推理配置 - 适合生产环境和高质量结果
# 使用与预训练模型完全匹配的参数以获得最佳表征质量

# 模型配置 - 必须与预训练模型laq_openx.pt完全匹配
model:
  dim: 1024                   # ✓ 不可调整 - 模型维度，与预训练权重匹配
  quant_dim: 32              # ✓ 不可调整 - 量化维度，与预训练权重匹配
  codebook_size: 8           # ✓ 不可调整 - 码本大小，与预训练权重匹配
  image_size: 256            # ✓ 不可调整 - 输入图像尺寸，与预训练权重匹配
  patch_size: 32             # ✓ 不可调整 - 图像块大小，与预训练权重匹配
  spatial_depth: 8           # ✓ 不可调整 - 空间Transformer层数，与预训练权重匹配
  temporal_depth: 8          # ✓ 不可调整 - 时间Transformer层数，与预训练权重匹配
  dim_head: 64               # ✓ 不可调整 - 注意力头维度，与预训练权重匹配
  heads: 16                  # ✓ 不可调整 - 注意力头数量，与预训练权重匹配
  code_seq_len: 4            # ✓ 不可调整 - 代码序列长度，与预训练权重匹配
  channels: 3                # ✓ 添加 - 输入图像通道数，RGB三通道
  checkpoint_path: "models/laq_openx.pt"

# 推理配置 - 平衡质量和速度（可调整参数）
inference:
  batch_size: 16              # ✓ 可调整 - 适中的批处理大小，平衡内存使用和速度
  num_workers: 4              # ✓ 可调整 - 标准的工作进程数
  pin_memory: true            # ✓ 可调整 - 启用内存锁定以优化GPU传输

# 数据配置
data:
  input_file: "data/smart_preprocessed_fixed/laq_dataset_hybrid.jsonl"
  output_file: "results/laq_high_quality_results.jsonl"

# 日志配置
log_level: "INFO"
log_file: "logs/laq_high_quality_inference.log"
